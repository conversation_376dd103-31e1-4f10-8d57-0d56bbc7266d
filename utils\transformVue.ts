let vueVersion: number
// #ifndef VUE3
export * from '@vue/composition-api'

// 添加 defineProps 和其他缺失的导出
export const defineProps = (props: any) => props
export const defineEmits = (emits: any) => emits
export const defineExpose = (expose: any) => expose

vueVersion = 2
// #endif

// #ifdef VUE3
export * from 'vue'
vueVersion = 3
// #endif
console.log(`vue version is ${vueVersion}`)
export { vueVersion }
