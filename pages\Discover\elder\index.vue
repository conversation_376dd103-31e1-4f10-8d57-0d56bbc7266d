<template>
	<view class="care_plan_page">
		<!-- <view class="edlder_list" v-if="edlderList.length > 1">
			<button type="primary" plain="true" v-for="(item,index) in edlderList" :key="`edlderList_${index}`" @click="getEdlder(item)">{{item.name}}</button>
		</view> -->
		<uni-card :title="item.timeSlot" :extra="type[item.progressStatus]" v-for="(item, index) in list" :key="index">
			<view>老人：{{ item.elderName }}</view>
			<view>护理员：{{ item.caregiverName }}</view>
			<view>护理动作：{{ item.activity }}</view>
		</uni-card>
	</view>
</template>

<script>
import { customNavigateTo } from '../../../utils/customNavigate'
import { careTaskRecorEdlder, getUserElderList } from "@/api/index"
export default {
	data() {
		return {
			list: [],
			accid: uni.getStorageSync("userInfo").accid,
			type: {
				0: '待开始',
				1: '进行中',
				2: '已完成',
				3: '未达标',
				4: '异常中断',
				5: '已取消'
			},
			edlderList: []
		};
	},
	created() {
		this.getEdlder()
		// getUserElderList().then(res => {
		// 	if(res.code == 200){
		// 		this.edlderList = res.rows
		// 		this.getEdlder(res.rows[0])
		// 	}
		// })

	},
	mounted() {

	},
	methods: {
		async getEdlder() {
			try {
				const { data, code } = await careTaskRecorEdlder();
				if (code === 200 && data) {
					this.list = data;
				} else {
					uni.showToast({
						title: '获取数据异常',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.showToast({
					title: '获取数据失败',
					icon: 'none'
				});
			}
		},
		// bindClick(item) {
		// 	console.log(item)
		// 	customNavigateTo({
		// 	  url: `/pages/Discover/carePlan/details?id=${item.id}`,
		// 	})
		// }
	}
}
</script>

<style lang="scss"></style>
