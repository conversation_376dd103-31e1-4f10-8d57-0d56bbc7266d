<template>
  <div class="empty-wrapper">
    <image
      class="empty-img"
      src="/static/empty.png"
      @error="handleImageError"
    />
    <div class="empty-text">{{ text }}</div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from '../utils/transformVue'
defineProps({
  text: {
    type: String,
    default: '',
  },
})

// 处理图片加载错误
const handleImageError = (event: any) => {
  console.log('Empty组件图片加载失败，使用默认图片')
  event.target.src = '/static/default-img.png'
}
</script>
<style lang="scss" scoped>
.empty-wrapper {
  margin: 75px 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  .empty-img {
    display: block;
    width: 125px;
    height: 100px;
  }

  .empty-text {
    display: block;
    color: #a6adb6;
    margin: 10px;
  }
}
</style>
