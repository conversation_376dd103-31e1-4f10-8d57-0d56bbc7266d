<template>
  <div class="call-page">
    <!-- 顶部状态栏 -->
    <div class="call-status-bar">
      <div class="call-status">{{ callStatusText }}</div>
      <div class="call-duration" v-if="callStatus === 'connected'">{{ formattedDuration }}</div>
    </div>

    <!-- 用户信息区域 -->
    <div class="call-user-info">
      <div class="call-avatar">
        <img :src="remoteUserAvatar || defaultAvatar" alt="avatar" class="avatar-img">
      </div>
      <div class="call-username">{{ remoteUserName }}</div>
    </div>

    <!-- 视频区域 (仅在视频通话时显示) -->
    <div class="call-video-container" v-if="callType === 2 && callStatus === 'connected'">
      <!-- 远程视频 -->
      <div class="remote-video-view">
        <!-- #ifdef APP-PLUS -->
        <NertcRemoteView
          :userID="remoteUserId"
          @load="handleRemoteViewLoad"
          style="width: 100%; height: 100%;"
        />
        <!-- #endif -->
      </div>
      <!-- 本地视频 (小窗口) -->
      <div class="local-video-view">
        <!-- #ifdef APP-PLUS -->
        <NertcLocalView
          :viewID="imOptions.accid"
          @load="handleLocalViewLoad"
          style="width: 100%; height: 100%;"
        />
        <!-- #endif -->
      </div>
    </div>

    <!-- 通话控制按钮 -->
    <div class="call-controls">
      <!-- 未接通时的按钮 -->
      <template v-if="callStatus !== 'connected'">
        <div class="call-action-row">
          <div class="call-action-btn decline-btn" @tap="handleDecline">
            <Icon type="icon-call-hangup" :size="30"></Icon>
            <div class="btn-text">{{ t('declineText') }}</div>
          </div>
          <div class="call-action-btn accept-btn" @tap="handleAccept">
            <Icon :type="callType === 1 ? 'icon-audio-call' : 'icon-video-call'" :size="30"></Icon>
            <div class="btn-text">{{ t('acceptText') }}</div>
          </div>
        </div>
      </template>

      <!-- 已接通时的按钮 -->
      <template v-else>
        <div class="call-action-row">
          <div class="call-action-btn" :class="{ 'active-btn': isMuted }" @tap="handleToggleMute">
            <Icon :type="isMuted ? 'icon-call-mute-active' : 'icon-call-mute'" :size="24"></Icon>
            <div class="btn-text">{{ t('muteText') }}</div>
          </div>

          <div class="call-action-btn" :class="{ 'active-btn': isSpeakerOn }" @tap="handleToggleSpeaker">
            <Icon :type="isSpeakerOn ? 'icon-call-speaker-active' : 'icon-call-speaker'" :size="24"></Icon>
            <div class="btn-text">{{ t('speakerText') }}</div>
          </div>

          <div class="call-action-btn" v-if="callType === 2" @tap="handleSwitchCamera">
            <Icon type="icon-call-switch-camera" :size="24"></Icon>
            <div class="btn-text">{{ t('switchCameraText') }}</div>
          </div>
        </div>

        <div class="call-action-row">
          <div class="call-action-btn hangup-btn" @tap="handleHangup">
            <Icon type="icon-call-hangup" :size="30"></Icon>
            <div class="btn-text">{{ t('hangupText') }}</div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from '../../utils/transformVue'
import Icon from '../../components/Icon.vue'
import { t } from '../../utils/i18n'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import callService from '../../services/CallService';
import { STORAGE_KEY } from 'utils/constants'
import Config from '../../utils/config'
// #ifdef APP-PLUS
import NertcLocalView from "../../NERtcUniappSDK-JS/nertc-view/NertcLocalView";
import NertcRemoteView from "../../NERtcUniappSDK-JS/nertc-view/NertcRemoteView";
// #endif
import { getImToken, getPermissionKey } from '../../api/resource';
// 通话类型：1-音频通话，2-视频通话
const callType = ref(1)
// 通话状态：calling-呼叫中，ringing-响铃中，connected-已接通，ended-已结束
const callStatus = ref('calling')
// 远程用户信息
const remoteUserName = ref('用户名')
const remoteUserAvatar = ref('')
const remoteUserId = ref('')
const defaultAvatar = '/static/images/default-avatar.png'

// 通话控制状态
const isMuted = ref(false)
const isSpeakerOn = ref(false)
const callStartTime = ref(0)
const callDuration = ref(0)
let timerInterval: any = null

// 是否已初始化引擎
const isSetup = ref(false)

const imOptions = uni.getStorageSync(STORAGE_KEY)

// 计算属性：通话状态文本
const callStatusText = computed(() => {
  switch (callStatus.value) {
    case 'calling':
      return callType.value === 1 ? t('audioCallingText') : t('videoCallingText')
    case 'ringing':
      return t('ringingText')
    case 'connected':
      return callType.value === 1 ? t('audioConnectedText') : t('videoConnectedText')
    case 'ended':
      return t('callEndedText')
    default:
      return ''
  }
})

// 计算属性：格式化通话时长
const formattedDuration = computed(() => {
  const seconds = callDuration.value
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60

  const formattedMinutes = minutes < 10 ? `0${minutes}` : `${minutes}`
  const formattedSeconds = remainingSeconds < 10 ? `0${remainingSeconds}` : `${remainingSeconds}`

  return `${formattedMinutes}:${formattedSeconds}`
})

// 处理接听通话
const handleAccept = () => {
  callStatus.value = 'connected';
  startCallTimer();

  // 如果引擎未初始化，先初始化
  if (!isSetup.value) {
    initCallEngine();
  }

  // 加入通话频道
  joinChannel();
}

// 处理拒绝通话
const handleDecline = () => {
  callStatus.value = 'ended';

  // 如果引擎已初始化，离开频道
  if (isSetup.value) {
    callService.leaveChannel();
  }

  setTimeout(() => {
    uni.navigateBack();
  }, 1000);
}

// 处理挂断通话
const handleHangup = () => {
  // 如果引擎已初始化，离开频道
  if (isSetup.value) {
    if (callType.value === 2) {
      callService.stopPreview();
    }
    callService.leaveChannel();
  }

  callStatus.value = 'ended';
  stopCallTimer();
  setTimeout(() => {
    uni.navigateBack();
  }, 1000);
}

// 处理静音切换
const handleToggleMute = () => {
  isMuted.value = !isMuted.value;

  if (isSetup.value) {
    callService.muteLocalAudio(isMuted.value);
  }

  uni.showToast({
    title: isMuted.value ? '已静音' : '已取消静音',
    icon: 'none'
  });
}

// 处理扬声器切换
const handleToggleSpeaker = () => {
  isSpeakerOn.value = !isSpeakerOn.value;

  if (isSetup.value) {
    callService.setSpeakerphoneOn(isSpeakerOn.value);
  }

  uni.showToast({
    title: isSpeakerOn.value ? '已切换到扬声器' : '已切换到听筒',
    icon: 'none'
  });
}

// 处理摄像头切换
const handleSwitchCamera = () => {
  if (callType.value !== 2) {
    uni.showToast({
      title: '仅视频通话可切换摄像头',
      icon: 'none'
    });
    return;
  }

  if (isSetup.value) {
    callService.switchCamera();
  }

  uni.showToast({
    title: t('cameraSwitchedText'),
    icon: 'none'
  });
}

// 开始计时器
const startCallTimer = () => {
  callStartTime.value = Math.floor(Date.now() / 1000);
  timerInterval = setInterval(() => {
    const now = Math.floor(Date.now() / 1000);
    callDuration.value = now - callStartTime.value;
  }, 1000);
}

// 停止计时器
const stopCallTimer = () => {
  if (timerInterval) {
    clearInterval(timerInterval);
    timerInterval = null;
  }
}

// 初始化通话引擎
const initCallEngine = () => {
  if (isSetup.value) return;

  // 初始化引擎
  callService.setupEngine();

  // 注册事件监听
  callService.addEventListener();

  // 注册自定义事件监听
  registerCallServiceEvents();

  // 设置本地视频画布
  callService.setupLocalVideoCanvas();

  // 请求权限
  callService.requestPermissions();

  // 如果是视频通话，启用视频
  if (callType.value === 2) {
    callService.enableLocalVideo(true);
    callService.startPreview();
  }

  // 设置默认扬声器状态（视频通话默认开启扬声器，音频通话默认关闭）
  isSpeakerOn.value = callType.value === 2;
  callService.setSpeakerphoneOn(isSpeakerOn.value);

  // 默认不静音
  isMuted.value = false;
  callService.muteLocalAudio(false);

  isSetup.value = true;
}

// 注册callService事件监听
const registerCallServiceEvents = () => {
  // 加入频道事件
  callService.on('joinChannel', (data: any) => {
    if (data.result === 0) {
      callStatus.value = 'connected';
      startCallTimer();
    }
  });

  // 离开频道事件
  callService.on('leaveChannel', () => {
    stopCallTimer();
  });

  // 用户离开事件
  callService.on('userLeave', () => {
    handleHangup();
  });
}

// 加入通话频道
const joinChannel = async () => {
  if (!isSetup.value) {
    console.error('引擎未初始化，无法加入频道');
    return;
  }

  try {
    // 定义频道名称
    const channelName = '通话';

    // 获取音视频通话Token，传递频道名称和有效期
    const tokenRes = await getImToken({
      channelName: channelName,
      ttlSec: '3600' // 设置token有效期为1小时
    });
    if (tokenRes.code !== 200 || !tokenRes.data) {
      uni.showToast({
        title: '获取通话Token失败',
        icon: 'none'
      });
      return;
    }

    // 获取权限密钥，传递频道名称、权限密钥和有效期
    const keyRes = await getPermissionKey({
      channelName: channelName,
      permSecret: Config.PERM_SECRET, // 使用配置文件中的权限密钥
      ttlSec: '3600', // 设置权限密钥有效期为1小时
      privilege: '0' // 默认权限值
    });
    if (keyRes.code !== 200 || !keyRes.data) {
      uni.showToast({
        title: '获取权限密钥失败',
        icon: 'none'
      });
      return;
    }

    // 保存token和密钥
    const rtcToken = tokenRes.data;
    const permissionKey = keyRes.data;

    console.log('加入通话频道');
    console.log('[NERTC-APP] joinChannel() 房间信息:', {
      token: rtcToken,
      channelName: '通话',
      myUid: imOptions.accid
    });

    callService.joinChannel({
      token: rtcToken, // 使用新获取的token
      channelName: '通话', // 自定义房间名称
      myUid: imOptions.accid
    });
  } catch (error) {
    console.error('加入频道失败:', error);
    uni.showToast({
      title: '加入通话失败',
      icon: 'none'
    });
  }
}

// 处理本地视图加载完成
const handleLocalViewLoad = () => {
  console.log('本地视图加载完成');
  if (isSetup.value && callType.value === 2) {
    try {
      // 设置本地视频画布
      callService.setupLocalVideoCanvas({
        view: imOptions.accid, // 添加view参数，对应NertcLocalView的viewID
        isMediaOverlay: false
      });

      // 开始预览
      callService.startPreview();
    } catch (error) {
      console.error('设置本地视频失败:', error);
      uni.showToast({
        title: '视频初始化失败',
        icon: 'none'
      });
    }
  }
}

// 处理远程视图加载完成
const handleRemoteViewLoad = () => {
  console.log('远程视图加载完成');
  if (isSetup.value && callType.value === 2 && remoteUserId.value) {
    try {
      // 设置远程视频画布
      callService.setupRemoteVideoCanvas(remoteUserId.value, {
        view: remoteUserId.value, // 添加view参数，对应NertcRemoteView的viewID
        isMediaOverlay: false
      });

      // 添加延迟，确保视图已经准备好
      setTimeout(() => {
        try {
          // 订阅远程视频
          callService.subscribeRemoteVideo(remoteUserId.value);
        } catch (error) {
          console.error('订阅远程视频失败:', error);
          uni.showToast({
            title: '远程视频订阅失败',
            icon: 'none'
          });
        }
      }, 100);
    } catch (error) {
      console.error('设置远程视频失败:', error);
      uni.showToast({
        title: '远程视频初始化失败',
        icon: 'none'
      });
    }
  }
}

// 页面加载时获取参数
onLoad((options: any = {}) => {
  if (options?.type) {
    callType.value = parseInt(options.type);
  }
  if (options?.name) {
    remoteUserName.value = options.name;
  }
  if (options?.avatar) {
    remoteUserAvatar.value = options.avatar;
  }
  if (options?.userId) {
    remoteUserId.value = options.userId;
  }
  if (options?.status) {
    callStatus.value = options.status;
    if (options.status === 'connected') {
      startCallTimer();
    }
  }

  // 初始化通话引擎
  initCallEngine();

  // 如果状态是已连接，加入频道
  if (callStatus.value === 'connected') {
    joinChannel();
  }
})

// 页面卸载时清理资源
onUnload(() => {
  // 停止计时器
  stopCallTimer();

  // 如果引擎已初始化，销毁引擎
  if (isSetup.value) {
    callService.destroyEngine();
    isSetup.value = false;
  }
})
</script>

<style lang="scss" scoped>
.call-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #1a1a1a;
  color: #ffffff;
}

.call-status-bar {
  width: 100%;
  padding: 40px 0 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.call-status {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

.call-duration {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.call-user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 60px;
}

.call-avatar {
  width: 120px;
  height: 120px;
  border-radius: 60px;
  overflow: hidden;
  background-color: #333;
  margin-bottom: 20px;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.call-username {
  font-size: 24px;
  font-weight: 500;
}

.call-video-container {
  position: relative;
  width: 100%;
  flex: 1;
  margin: 20px 0;
}

.remote-video-view {
  width: 100%;
  height: 100%;
  background-color: #333;
}

.local-video-view {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 100px;
  height: 150px;
  background-color: #555;
  border-radius: 8px;
  overflow: hidden;
}

.call-controls {
  width: 100%;
  padding: 20px;
  margin-top: auto;
  margin-bottom: 40px;
}

.call-action-row {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.call-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 20px;
  cursor: pointer;
}

.btn-text {
  margin-top: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.active-btn {
  color: #4883EA;
}

.active-btn .btn-text {
  color: #4883EA;
}

.decline-btn, .hangup-btn {
  background-color: #FF3B30;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.accept-btn {
  background-color: #34C759;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.decline-btn .btn-text,
.accept-btn .btn-text,
.hangup-btn .btn-text {
  position: absolute;
  bottom: -30px;
}
</style>