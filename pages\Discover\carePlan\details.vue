<template>
	<view class="care_plan_details">
		<div class="relation_box" v-if="data">
			<div class="label_box">
				<label>姓名：</label>
				<span>{{data.elderName}}</span>
			</div>
			<div class="label_box">
				<label>年龄：</label>
				<span>{{data.elderInfo.age}}</span>
			</div>
			<div class="label_box">
				<label>床号：</label>
				<span>{{data.elderInfo.bed_num}}</span>
			</div>
			<div class="label_box">
				<label>自理能力：</label>
				<span>{{abilitys[data.elderInfo.latest_eval_grade]}}</span>
			</div>
			<div class="label_box">
				<label>护理动作：</label>
				<span>{{data.activity}}</span>
			</div>
			<div class="label_box">
				<label>注意事项：</label>
				<span>{{data.elderInfo.note || '无'}}</span>
			</div>
		</div>
		<button type="primary" class="movement_video" @click="startTask">开始执行照顾任务</button>
		<!-- <button type="primary" class="movement_video" plain>护理动作视频介绍</button> -->
	</view>
</template>

<script>
	import {
		careTaskRecord,
		careTaskRecordstartExecuting
	} from "@/api/index"
	export default {
		data() {
			return {
				data: null,
				id:null,
				abilitys: {
					1: '能力完好',
					2: '能力轻度受损(轻度失能)',
					3: '能力中度受损(中度失能)',
					4: '能力重度受损(重度失能)',
					5: '能力完全丧失(完全失能)',
				}
			};
		},
		onLoad: function(option) { //option为object类型，会序列化上个页面传递的参数
			this.id = option.id
			careTaskRecord({
				id: option.id
			}).then(res => {
				console.log(res)
				// console.log(uni.getStorageSync("x_chat_user_info"))
				this.data = res.data
			})
		},
		methods:{
			startTask(){
				careTaskRecordstartExecuting({id: this.id}).then(res => {
					console.log(res)
					// 调用API后跳转到指定APP
					// #ifdef APP-PLUS
					// plus.runtime.launchApplication(
					// 	{
					// 		pname: 'com.example.changxy', // 要跳转的应用包名
					// 	},
					// 	function(e) {
					// 		uni.showToast({
					// 			title: '未安装目标应用',
					// 			icon: 'none'
					// 		});
					// 	}
					// );
					// #endif
					
					// #ifndef APP-PLUS
					uni.showToast({
						title: res.message,
						icon: 'success',
						duration: 2000
					});
					// #endif
				})
			}
		}
	}
</script>

<style lang="scss">
	.care_plan_details {
		padding: 15px;

		.movement_video {
			margin-top: 50px;
		}
	}

	.relation_box {
		padding: 15px;
		background-color: #fff;
		border-radius: 10px;
		box-shadow: 0px 0px 5px rgba(0, 0, 0, .15);

		.label_box {
			display: flex;
			height: 30px;
			align-items: center;

			&.right {
				justify-content: space-between;
			}

			label {
				color: #999;
				font-size: 14px;
			}

			span {
				font-size: 14px;
				color: #333;
			}
		}
	}
</style>