<template>
  <div>
    <div class="navigation-bar">
    </div>
    <div class="login-form-container">
      <div class="login-tabs">
        <span v-for="item in loginTabs.list" :key="item.key"
          :class="['login-tab', { active: loginTabs.active === item.key }]" @click="loginTabs.active = item.key">
          <span>{{ item.title }}</span>
        </span>
      </div>
      <div class="login-tips">{{ i18n.loginTips }}</div>
      <div class="login-form">
        <FormInput className="login-form-input" type="tel" :value="loginForm.mobile"
          @updateModelValue="val => loginForm.mobile = val" :placeholder="i18n.mobilePlaceholder" :allow-clear="true"
          :rule="mobileInputRule">
          <template #addonBefore>
            <span class="phone-addon-before">+86</span>
          </template>
        </FormInput>
        <FormInput className="login-form-input" type="tel" :value="loginForm.smsCode"
          @updateModelValue="val => loginForm.smsCode = val" :placeholder="i18n.smsCodePlaceholder"
          :rule="smsCodeInputRule">
          <template #addonAfter>
            <span :class="['sms-addon-after', { 'disabled': isRequestingCode || (smsCount > 0 && smsCount < 60) }]"
              @click="(!isRequestingCode && !(smsCount > 0 && smsCount < 60)) ? showCaptchaModal() : null">{{
                smsText
              }}</span>
          </template>
        </FormInput>
      </div>

      <!-- 隐私政策协议勾选框 -->
      <div class="privacy-policy-checkbox">
        <checkbox-group @change="onPrivacyPolicyChange">
          <checkbox :value="'privacy'" :checked="privacyPolicyChecked" color="#337EFF" />
        </checkbox-group>
        <div class="policy-links-container">
          <text class="privacy-policy-text" @tap="togglePrivacyPolicy">{{ i18n.privacyPolicyText }}</text>
          <text class="privacy-policy-link" @tap="goToPrivacyPolicy">{{ i18n.privacyPolicyLinkText }}</text>
          <text class="privacy-policy-link" @tap="goToUserAgreement">{{ i18n.userAgreementLinkText }}</text>
        </div>
      </div>
    </div>
    <button class="login-btn" :class="{'login-btn-disabled': !privacyPolicyChecked}" @click="handleLoginClick()">{{ i18n.loginBtnTitle }}</button>

    <!-- 图形验证码弹窗 -->
    <Modal :visible="captchaModalVisible" :title="'图形验证码'" :confirm-text="'确定'" :cancel-text="'取消'"
      @confirm="startSmsCount" @cancel="closeCaptchaModal">
      <div class="captcha-container">
        <div class="captcha-input-row">
          <div class="captcha-image-container" @click="showCaptchaModal">
            <image v-if="captchaImage" :src="captchaImage" class="captcha-image" />
            <div v-else class="captcha-loading">加载中...</div>
          </div>
          <FormInput className="captcha-input" type="text" :value="captchaValue"
            @updateModelValue="val => captchaValue = val" placeholder="请输入验证码" />
        </div>
      </div>
    </Modal>
  </div>
</template>

<script lang="ts" setup>
// @ts-ignore
import { ref, reactive, computed } from '../../../utils/transformVue'
import i18n from '../i18n/zh-cn'
import { getLoginSmsCode, loginRegisterByCode } from '../utils/api';
import { getImUserInfo } from "@/api/index"
import { CryptoUtil } from '@/utils/crypto'
import { validateMobile } from '@/utils/common'
import {
  sendCode,
  appPhoneLogin,
  companyList,
  geAscFriendStatus,
  getCaptcha,
  verifyCaptcha as verifyImageCaptcha
} from "@/api/index.js"
// @ts-ignore
import FormInput from './form-input.vue'
import Modal from '@/components/Modal.vue'
import {
  CLIENT_ID,
  SYTETEM_TYPE,
  TENANT_ID,
  USER_TYPE
} from "@/utils/config"

const isMyWay = true;
const privacyPolicyChecked = ref(false);

const mobileInputRule = {
  regExp: (/^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/),
  reg: 'phone',
  message: i18n.mobileErrorMsg,
  trigger: 'blur'
}
const smsCodeInputRule = {
  reg: 'smsCode',
  regExp: /^\d+$/,
  message: i18n.smsErrorMsg,
  trigger: 'blur'
}

const smsCount = ref(60)
const asyTimer: any = ref(null)
// 添加一个标志位，用于防止重复请求
const isRequestingCode = ref(false)

// 图形验证码相关
const captchaModalVisible = ref(false)
const captchaImage = ref('')
// 输入的图形验证码结果
const captchaValue = ref('')
const uuid = ref('')
const publicKey = ref('')


// 添加防抖函数
function debounce(func, wait) {
  let timeout;
  return function () {
    const context = this;
    const args = arguments;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
}

// 生成唯一请求ID
function generateRequestId() {
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// 当前请求ID
const currentRequestId = ref('')
const loginTabs = reactive({
  active: 0,
  list: [
    { key: 0, title: i18n.loginTitle },
  ]
})
const loginForm = reactive({
  mobile: '',
  smsCode: '',
  dept: ''
})
const deptList = ref([])


const smsText = computed(() => {
  if (smsCount.value > 0 && smsCount.value < 60) {
    return smsCount.value + i18n.smsCodeBtnTitleCount
  } else {
    return i18n.smsCodeBtnTitle
  }
})
// 原始获取验证码函数
async function _startSmsCount() {
  // 防止重复提交：检查是否已经在请求中或者在计时中
  if (isRequestingCode.value) {
    console.log('验证码请求正在进行中，请勿重复点击')
    return
  }

  // 如果已经在计时中，直接返回
  if (smsCount.value > 0 && smsCount.value < 60) {
    console.log('验证码倒计时中，请稍后再试')
    return
  }

  // 验证手机号是否填写
  if (!loginForm.mobile) {
    uni.showToast({
      title: i18n.mobileErrorMsg,
      icon: 'none'
    })
    return
  }

  // 验证图形验证码是否填写
  if (!captchaValue.value) {
    uni.showToast({
      title: '请输入图形验证码',
      icon: 'none'
    })
    return
  }

  // 设置请求标志位，防止重复请求
  isRequestingCode.value = true

  uni.showLoading({
    mask: true
  })

  // 生成新的请求ID
  currentRequestId.value = generateRequestId()
  const requestId = currentRequestId.value

  console.log(`发起验证码请求: ${requestId}, 手机号: ${loginForm.mobile}`)

  try {
    if (isMyWay) {
      // 使用UUID加密手机号
      const encryptedPhone = CryptoUtil.encryptWithSM2(loginForm.mobile, publicKey.value)
      // 添加请求ID到请求参数中
      const requestParams = {
        phoneNumber: encryptedPhone,
        requestId: requestId, // 添加请求ID
        uuid: uuid.value,
        code: captchaValue.value
      }
      console.log('发送验证码', requestParams);

      // 添加请求超时处理
      const requestPromise = sendCode(requestParams)

      // 设置超时处理，5秒后自动重置状态
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('请求超时，请检查网络连接'))
        }, 5000) // 5秒超时，减少等待时间
      })

      // 使用Promise.race来处理超时
      const response = await Promise.race([requestPromise, timeoutPromise])

      // 隐藏加载动画
      uni.hideLoading()

      console.log(`验证码请求响应: ${requestId}`, response)

      // 检查这个响应是否对应当前请求ID，防止响应错乱
      if (requestId !== currentRequestId.value) {
        console.log(`请求ID不匹配，忽略响应: ${requestId} vs ${currentRequestId.value}`)
        return
      }

      // 检查响应状态
      if (response && response.code !== 200) {
        uni.showToast({
          title: response.msg || i18n.smsCodeFailMsg,
          icon: 'none'
        })
        // 重置请求标志位
        isRequestingCode.value = false
        return // 如果请求失败，不启动计时器
      }
      closeCaptchaModal()
      // 显示发送成功提示
      uni.showToast({
        title: '验证码已发送',
        icon: 'success',
        duration: 2000
      })
    } else {
      // 如果使用原始方式，不传递requestId
      await getLoginSmsCode({ mobile: loginForm.mobile })
      closeCaptchaModal()
      uni.showToast({
        title: '验证码已发送',
        icon: 'success',
        duration: 2000
      })
    }

    // 请求成功，启动计时器
    smsCount.value = 59 // 从59开始倒计时
    const timer = setInterval(() => {
      if (smsCount.value > 0) {
        smsCount.value--
      } else {
        clearInterval(timer)
        smsCount.value = 60
        // 计时结束后重置请求标志位（以防万一）
        isRequestingCode.value = false
      }
    }, 1000)

    // 请求成功后重置请求标志位
    isRequestingCode.value = false

  } catch (error: any) {
    console.error(`验证码请求错误 (${requestId}):`, error)

    // 检查这个错误是否对应当前请求ID，防止错误处理错乱
    if (requestId !== currentRequestId.value) {
      console.log(`请求ID不匹配，忽略错误: ${requestId} vs ${currentRequestId.value}`)
      return
    }

    // 处理错误
    let msg = error.errMsg || error.msg || error.message || i18n.smsCodeFailMsg
    if (msg.startsWith('request:fail')) {
      msg = i18n.smsCodeNetworkErrorMsg
    }

    // 检查是否是500错误
    if (error.statusCode === 500 || (error.data && error.data.code === 500)) {
      msg = '服务器错误，请稍后再试'
    }

    uni.showToast({
      title: msg,
      icon: 'none'
    })

    // 重置请求标志位，允许用户重新请求
    isRequestingCode.value = false
    return
  }
}

// 图形验证码相关方法
// 显示图形验证码弹窗
async function showCaptchaModal() {
  // 验证手机号是否填写
  if (!loginForm.mobile) {
    uni.showToast({
      title: i18n.mobileErrorMsg,
      icon: 'none'
    })
    return
  }

  // 验证手机号格式
  if (!mobileInputRule.regExp.test(loginForm.mobile)) {
    uni.showToast({
      title: i18n.mobileErrorMsg,
      icon: 'none'
    })
    return
  }

  // 重置验证码输入
  captchaValue.value = ''
  captchaImage.value = ''

  try {
    // 显示加载中提示
    uni.showLoading({
      title: '加载中...',
      mask: true
    })

    // 先获取图形验证码
    const { data } = await getCaptcha()
    const { uuid: id, img, key } = data
    uuid.value = id
    captchaImage.value = 'data:image/png;base64,' + img
    publicKey.value = key  //保存服务器返回的公钥
    // 关闭加载提示
    uni.hideLoading()
    // 获取成功后再显示弹窗
    captchaModalVisible.value = true
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '获取验证码失败，请重试',
      icon: 'none'
    })
  }
}

// 关闭图形验证码弹窗
function closeCaptchaModal() {
  captchaModalVisible.value = false,
    captchaValue.value = ''
}

// 使用防抖包装获取验证码函数
const startSmsCount = debounce(_startSmsCount, 300)
// 登录
async function submitLoginForm() {
  // 验证手机号是否填写
  if (!loginForm.mobile) {
    uni.showToast({
      title: i18n.mobileErrorMsg,
      icon: 'none'
    })
    return
  }

  // 验证手机号和验证码格式
  if (!mobileInputRule.regExp.test(loginForm.mobile) || !smsCodeInputRule.regExp.test(loginForm.smsCode)) {
    uni.showToast({
      title: i18n.mobileOrSmsCodeErrorMsg,
      icon: 'none'
    })
    return
  }

  // 隐私政策验证已在 handleLoginClick 中处理
  try {
    // const res = await loginRegisterByCode(loginForm)
    const params = {
      phoneNumber: loginForm.mobile,
      smsCode: loginForm.smsCode,
      grantType: 'asms',
      clientId: CLIENT_ID,
      systemType: SYTETEM_TYPE,
      tenantId: TENANT_ID,
      userType: USER_TYPE
    }
    const app = getApp()
    if (isMyWay) {
      const res = await appPhoneLogin(params)
      if (res?.code != 200) {
        const msg = res?.msg || this.$common.errmsg
        this.$common.tipMsg(msg)
        return
      }
      uni.setStorageSync('token', res.data.access_token)
      uni.setStorageSync('userInfo', res.data.user)
         // 获取并保存聊天用户信息
         try {
        const imUserResponse = await getImUserInfo(res.data.user.accid)
        if (imUserResponse.code === 200) {
          console.log('保存成功1');
          
          uni.setStorageSync('x_chat_user_info', imUserResponse.data)
        }
      } catch (error) {
        console.error('获取聊天用户信息失败:', error)
      }
      // 判断是否需要同步好友
      if (res?.data?.user?.isAscFriend) {
        uni.showLoading({
          title: '正在同步好友数据',
          mask: true
        })
        geAscFriendStatusFun(res);
        asyTimer.value = setInterval(() => {
          geAscFriendStatusFun(res)
        }, 5000)
      } else {
        app.initNim({ account: res.data.user.accid, token: res.data.user.token })
      }
    } else {
      const res = await loginRegisterByCode(loginForm)
      app.initNim({ account: res.imAccid, token: res.imToken })
    }
  } catch (error: any) {
    let msg = error.errMsg || error.msg || error.message || i18n.smsCodeFailMsg
    if (msg.startsWith('request:fail')) {
      msg = i18n.loginNetworkErrorMsg
    }
    uni.showToast({
      title: msg,
      icon: 'none'
    })
  }
}

const geAscFriendStatusFun = (opt) => {
  const app = getApp()
  geAscFriendStatus().then(res => {
    if (res?.data || res?.code !== 200) {
      app.initNim({ account: opt.data.user.accid, token: opt.data.user.token })
      if (asyTimer.value) {
        uni.hideLoading()
        clearInterval(asyTimer.value)
        asyTimer.value = null
      }
    }
  })
}

const companyListFun = () => {
  const params = {
    clientid: CLIENT_ID
  }
  companyList(params).then(res => {
    console.log(res);
    // deptList.value =
  })
}

// companyListFun();

// 处理隐私政策勾选状态变化
function onPrivacyPolicyChange(e) {
  // checkbox-group 的 change 事件返回的是一个数组
  privacyPolicyChecked.value = e.detail.value.includes('privacy');
  console.log('隐私政策勾选状态:', privacyPolicyChecked.value);
}

// 点击文本时切换勾选状态
function togglePrivacyPolicy() {
  privacyPolicyChecked.value = !privacyPolicyChecked.value;
  console.log('点击文本切换隐私政策勾选状态:', privacyPolicyChecked.value);
}

// 跳转到隐私政策页面
function goToPrivacyPolicy() {
  uni.navigateTo({
    url: '/pages/Privacy/privacy-policy'
  });
}

// 跳转到用户服务协议页面
function goToUserAgreement() {
  uni.navigateTo({
    url: '/pages/Privacy/user-agreement'
  });
}

// 处理登录按钮点击
function handleLoginClick() {
  // 验证是否勾选隐私政策
  if (!privacyPolicyChecked.value) {
    uni.showToast({
      title: i18n.privacyPolicyRequiredMsg,
      icon: 'none'
    });
    return;
  }

  // 如果已勾选，则继续登录流程
  submitLoginForm();
}

</script>

<style lang="scss" scoped>
$primary-color: #337EFF;

.navigation-bar {
  height: 80px;
}

.login-form-container {
  padding: 0 30px;
}

.login-tab {
  display: inline-block;
  font-size: 22px;
  line-height: 31px;
  color: #666B73;
  margin-right: 20px;
  margin-bottom: 20px;

  &.active {
    color: #222222;
    font-weight: bold;
  }

  .login-tab-active-line {
    display: block;
    width: 80px;
    height: 2px;
    background: $primary-color;
    margin: 5px auto;
    border-radius: 2px;
  }
}

.login-tips {
  font-size: 14px;
  line-height: 20px;
  color: #999999;
  margin-bottom: 20px;
}

.phone-addon-before {
  color: #999999;
  border-right: 1px solid #999999;
  padding: 0 5px;
  margin-right: 10px;
}

.sms-addon-after {
  color: $primary-color;
  cursor: pointer;

  &.disabled {
    color: #666B73;
    cursor: not-allowed;
    opacity: 0.7;
  }
}

.login-form-input {
  margin-bottom: 20px;
}

.privacy-policy-checkbox {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  margin-top: 10px;
  padding-left: 5px;
}

.privacy-policy-checkbox checkbox-group {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.privacy-policy-text {
  font-size: 12px;
  color: #666B73;
  margin-left: 5px;
  cursor: pointer;
}

.privacy-policy-link {
  font-size: 12px;
  color: $primary-color;
  text-decoration: none;
  cursor: pointer;
  margin-right: 2px;
}

.policy-links-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.login-btn {
  height: 50px;
  width: 90%;
  background: $primary-color;
  border-radius: 8px;
  color: #fff;
  margin-top: 30px;
}

.login-btn-disabled {
  background: #CCCCCC;
  color: #FFFFFF;
}

// 图形验证码相关样式
.captcha-container {
  padding: 16px;

  // 覆盖Modal组件的样式
  :deep(.content) {
    width: 320px; // 调整弹窗宽度
  }
}

.captcha-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.captcha-title {
  font-size: 14px;
  color: #333;
}

.captcha-refresh {
  font-size: 14px;
  color: $primary-color;
  cursor: pointer;
}

.captcha-input-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px; // 设置图形验证码和输入框之间的间距
}

.captcha-image-container {
  flex: 0 0 auto; // 不伸缩，保持原始大小
  width: 120px; // 设置验证码图片宽度
  height: 36px; // 调整高度更低一些
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #E1E6E8;
  border-radius: 4px;
  overflow: hidden;
}

.captcha-image {
  width: 100%;
  height: 100%;
  object-fit: cover; // 确保图片正确填充容器
}

.captcha-loading {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 12px;
}

.captcha-input {
  flex: 1; // 让输入框占据剩余空间
  margin-bottom: 0;
  display: flex;
  align-items: center;

  // 调整输入框内部样式
  :deep(.form-input-item) {
    height: 36px;
    display: flex;
    align-items: center;
  }
}
</style>
