import {
	NERTCLogLevel,
	NERTCRenderMode,
	NERTCMirrorMode,
	NERtcVideoStreamType,
	NERtcVideoFrameRate,
	NERtcVideoCropMode,
	NERtcDegradationPreference,
	NERtcVideoOutputOrientationMode,
	NERtcVideoProfileType,
	NERtcRemoteVideoStreamType,
	NERTCAudioDevice,
	NERTCAudioDeviceType,
	NERTCAudioDeviceState,
	NERTCVideoDeviceState,
	NERTCConnectionType,
	NERTCErrorCode,
	NERtcAudioVolumeInfo,
	NERTCAudioProfile,
	NERTCAudioScenario,
	NERTCChannelProfile,
	NERTCUserRole,
	NERtcSubStreamContentPrefer
} from '@/NERtcUniappSDK-JS/lib/NERtcDefines';

let appkey = '6acf024e190215b685905444b6e57dd7';
let userID = Math.floor(Math.random() * 1000000).toString(5);
let token = ""
let channelName = 'uniapp'
let engineConfig = {
	appKey: appkey,
	logDir: '', // expected log directory
	logLevel: NERTCLogLevel.INFO,
}

let audioVolumeConfig = {
	captureVolume: 100,
	userPlaybackVolume: 100,
	channelPlaybackVolume: 100
}

//设置本地用户视图（setupLocalVideoCanvas）的配置参数
let LocalVideoCanvasConfig = {
	renderMode: NERTCRenderMode.Fit, // Fit表示应区域。视频尺寸等比缩放,保证所有区域被填满,视频超出部分会被裁剪
	mirrorMode: NERTCMirrorMode.AUTO, //AUTO表示使用默认值,由sdk控制
	isMediaOverlay: false //表示小画布置于大画布上面
}

//设置远端用户视图（setupRemoteVideoCanvas）的配置参数
let RemoteVideoCanvasConfig = {
	renderMode: NERTCRenderMode.Fit, // Fit表示应区域。视频尺寸等比缩放,保证所有区域被填满,视频超出部分会被裁剪
	mirrorMode: NERTCMirrorMode.AUTO, //AUTO表示使用默认值,由sdk控制
	isMediaOverlay: false //表示小画布置于大画布上面
}

//设置本端用户的视频辅流画布（setupLocalSubStreamVideoCanvas）的配置参数
let LocalSubStreamVideoCanvasConfig = {
	renderMode: NERTCRenderMode.Fit, // Fit表示应区域。视频辅流尺寸等比缩放,保证所有区域被填满,视频辅流超出部分会被裁剪
	mirrorMode: NERTCMirrorMode.AUTO, //AUTO表示使用默认值,由sdk控制
	isMediaOverlay: false //表示小画布置于大画布上面
}

//设置远端用户的视频辅流视图（setupRemoteSubStreamVideoCanvas）的配置参数
let RemoteSubStreamVideoCanvasConfig = {
	renderMode: NERTCRenderMode.Fit, // Fit表示应区域。视频尺寸等比缩放,保证所有区域被填满,视频超出部分会被裁剪
	mirrorMode: NERTCMirrorMode.AUTO, //AUTO表示使用默认值,由sdk控制
	isMediaOverlay: false, //表示小画布置于大画布上面
	//userID: userID //对方userID
}

//开启屏幕共享（startScreenCapture）的配置参数
let ScreenCaptureConfig = {
	maxProfile: NERtcVideoProfileType.Standard,
	frameRate: NERtcVideoFrameRate.FRAME_RATE_FPS_24, //设置帧率24
	minFrameRate: NERtcVideoFrameRate.FRAME_RATE_FPS_15, //设置最小帧率15
	bitrate: 2500, //设置屏幕共享码率1500kbps
	minBitrate: 1500, //设置屏幕共享码率最小为1000kbps
	contentPrefer: NERtcSubStreamContentPrefer.CONTENT_PREFER_DETAILS, //屏幕共享功能的编码策略倾向
}

//设置音频编码属性（setAudioProfile）的配置参数
let AudioProfileConfig = {
	profile: NERTCAudioProfile.STANDARD, //设置profile为标准模式
	scenario: NERTCAudioScenario.SPEECH ,//scenario为语音场景
}

//设置视频编码属性（setLocalVideoConfig）的配置参数
let VideoConfig = {
	videoStreamType: NERtcVideoStreamType.MAIN, // 表示是对视频主流的配置
	maxProfile: NERtcVideoProfileType.HD1080P, // 使用1080P高清配置
	frameRate: NERtcVideoFrameRate.FRAME_RATE_FPS_30, // 设置帧率30
	minFrameRate: NERtcVideoFrameRate.FRAME_RATE_FPS_15, // 设置最小帧率15
	bitrate: 3000, // 设置视频码率3000kbps
	minBitrate: 1500, // 设置视频码率最小为1500kbps
	width: 1280, // 设置视频宽为1280
	height: 720, // 设置视频高为720
	cropMode: NERtcVideoCropMode.CROP_16x9, // 视频采集宽高比为16:9
	degradationPreference: NERtcDegradationPreference.DEGRADATION_DEFAULT, // 带宽受限时的视频编码降级偏好为默认
	mirrorMode: NERTCMirrorMode.AUTO, // AUTO表示使用默认值
	orientationMode: NERtcVideoOutputOrientationMode.VIDEO_OUTPUT_ORIENTATION_MODE_ADAPTATIVE // （默认）该模式下 SDK 输出的视频方向与采集到的视频方向一致。接收端会根据收到的视频旋转信息对视频进行旋转
}


function getAppkey() {
	console.log('getAppkey() appkey: ', appkey)
	return getApp().globalData.appkey ?? appkey;
}

function setAppkey(data) {
	console.log('setAppkey() data: ', data)
	getApp().globalData.appkey = data || appkey;
}

function getToken() {
	return getApp().globalData.token ?? token;
}

function setToken (data) {
	getApp().globalData.token = data || token;
}

function getChannelName() {
	console.log('getChannelName()：', getApp().globalData.channelName ?? channelName)
	return getApp().globalData.channelName ?? channelName;
}

function setChannelName(data) {
	console.log('setChannelName() data: ', data)
	getApp().globalData.channelName = data || channelName
}

function getUserID() {
	return getApp().globalData.userID ?? userID;
}

function setUserID(data) {
	getApp().globalData.userID = data || userID;
}

function getEngineConfig() {
	return getApp().globalData.engineConfig ?? engineConfig;
}

function setEngineConfig(data) {
	getApp().globalData.engineConfig = data || engineConfig
}

function getAudioVolumeConfig() {
return getApp().globalData.audioVolumeConfig ?? audioVolumeConfig;
}

function setAudioVolumeConfig(data) {
	getApp().globalData.audioVolumeConfig = data || audioVolumeConfig
}

function getLocalVideoCanvasConfig(){
	return getApp().globalData.LocalVideoCanvasConfig ?? LocalVideoCanvasConfig;
}

function setLocalVideoCanvasConfig(data) {
	getApp().globalData.LocalVideoCanvasConfig = data || LocalVideoCanvasConfig
}

function getRemoteVideoCanvasConfig(){
	console.log('getRemoteVideoCanvasConfig: RemoteVideoCanvasConfig: ', JSON.stringify(RemoteVideoCanvasConfig) )
	return getApp().globalData.RemoteVideoCanvasConfig ?? RemoteVideoCanvasConfig;
}

function setRemoteVideoCanvasConfig(data) {
	getApp().globalData.RemoteVideoCanvasConfig = data || RemoteVideoCanvasConfig
}

function getLocalSubStreamVideoCanvasConfig(){
	return getApp().globalData.LocalSubStreamVideoCanvasConfig ?? LocalSubStreamVideoCanvasConfig;
}

function setLocalSubStreamVideoCanvasConfig(data) {
	getApp().globalData.LocalSubStreamVideoCanvasConfig = data || LocalSubStreamVideoCanvasConfig
}

function getRemoteSubStreamVideoCanvasConfig(){
	return getApp().globalData.RemoteSubStreamVideoCanvasConfig ?? RemoteSubStreamVideoCanvasConfig;
}

function setRemoteSubStreamVideoCanvasConfig(data) {
	getApp().globalData.RemoteVideoCanvasConfig = RemoteSubStreamVideoCanvasConfig || RemoteSubStreamVideoCanvasConfig
}

function getScreenCaptureConfig(){
	return getApp().globalData.ScreenCaptureConfig ?? ScreenCaptureConfig;
}

function setScreenCaptureConfig(data){
	console.log('setScreenCaptureConfig() data: ' , data)
	getApp().globalData.ScreenCaptureConfig = data || ScreenCaptureConfig
}

function getAudioProfileConfig(){
	return getApp().globalData.AudioProfileConfig ?? AudioProfileConfig;
}

function setAudioProfileConfig(data) {
	console.log('setAudioProfileConfig() data: ' , data)
	getApp().globalData.AudioProfileConfig = data || AudioProfileConfig
}

function getVideoConfig() {
	return getApp().globalData.VideoConfig ?? VideoConfig;
}

function setVideoConfig(data) {
	console.warn('setVideoConfig() data: ' , data)
	getApp().globalData.VideoConfig = data || VideoConfig
}

export default {
	getAppkey,
	setAppkey,
	getToken,
	setToken,
	getUserID,
	setUserID,
	getChannelName,
	setChannelName,
	getEngineConfig,
	setEngineConfig,
	getAudioVolumeConfig,
	setAudioVolumeConfig,
	getLocalVideoCanvasConfig,
	setLocalVideoCanvasConfig,
	getRemoteVideoCanvasConfig,
	setRemoteVideoCanvasConfig,
	getLocalSubStreamVideoCanvasConfig,
	setLocalSubStreamVideoCanvasConfig,
	getRemoteSubStreamVideoCanvasConfig,
	setRemoteSubStreamVideoCanvasConfig,
	getScreenCaptureConfig,
	setScreenCaptureConfig,
	getAudioProfileConfig,
	setAudioProfileConfig,
	getVideoConfig,
	setVideoConfig
}

