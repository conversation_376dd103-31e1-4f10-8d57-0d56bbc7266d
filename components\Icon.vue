<template>
  <view :class="className" :style="iconStyle">
    <!-- #ifdef APP-PLUS -->
    <image
      :src="url"
      :style="{
        width: (width || size) + 'px',
        height: (height || size) + 'px',
      }"
      class="icon"
    />
    <!-- #endif -->
    <!-- #ifndef APP-PLUS -->
    <img
      :src="url"
      :style="{
        width: (width || size) + 'px',
        height: (height || size) + 'px',
      }"
      class="icon"
    />
    <!-- #endif -->
  </view>
</template>

<script lang="ts" setup>
// svg 解决方案参考：https://github.com/iconfont-cli/mini-program-iconfont-cli/tree/master
// import { ref } from 'vue'
import { ref, computed } from '../utils/transformVue'
const props = defineProps({
  type: {
    type: String,
    required: true,
  },
  size: {
    type: Number,
    default: 16,
  },
  width: {
    type: Number,
  },
  height: {
    type: Number,
  },
  iconClassName: {
    type: String,
    default: null,
  },
  iconStyle: {
    type: Object,
    default: () => {},
  },
})
const urlMap = {
  'icon-a-1': '/static/icons/icon-a-1.png',
  'icon-a-2': '/static/icons/icon-a-2.png',
  'icon-a-3': '/static/icons/icon-a-3.png',
  'icon-a-4': '/static/icons/icon-a-4.png',
  'icon-a-5': '/static/icons/icon-a-5.png',
  'icon-a-6': '/static/icons/icon-a-6.png',
  'icon-a-7': '/static/icons/icon-a-7.png',
  'icon-a-8': '/static/icons/icon-a-8.png',
  'icon-a-9': '/static/icons/icon-a-9.png',
  'icon-a-10': '/static/icons/icon-a-10.png',
  'icon-a-11': '/static/icons/icon-a-11.png',
  'icon-a-12': '/static/icons/icon-a-12.png',
  'icon-a-13': '/static/icons/icon-a-13.png',
  'icon-a-14': '/static/icons/icon-a-14.png',
  'icon-a-15': '/static/icons/icon-a-15.png',
  'icon-a-16': '/static/icons/icon-a-16.png',
  'icon-a-17': '/static/icons/icon-a-17.png',
  'icon-a-18': '/static/icons/icon-a-18.png',
  'icon-a-19': '/static/icons/icon-a-19.png',
  'icon-a-20': '/static/icons/icon-a-20.png',
  'icon-a-21': '/static/icons/icon-a-21.png',
  'icon-a-22': '/static/icons/icon-a-22.png',
  'icon-a-23': '/static/icons/icon-a-23.png',
  'icon-a-24': '/static/icons/icon-a-24.png',
  'icon-a-25': '/static/icons/icon-a-25.png',
  'icon-a-26': '/static/icons/icon-a-26.png',
  'icon-a-27': '/static/icons/icon-a-27.png',
  'icon-a-28': '/static/icons/icon-a-28.png',
  'icon-a-29': '/static/icons/icon-a-29.png',
  'icon-a-30': '/static/icons/icon-a-30.png',
  'icon-a-31': '/static/icons/icon-a-31.png',
  'icon-a-32': '/static/icons/icon-a-32.png',
  'icon-a-33': '/static/icons/icon-a-33.png',
  'icon-a-34': '/static/icons/icon-a-34.png',
  'icon-a-35': '/static/icons/icon-a-35.png',
  'icon-a-36': '/static/icons/icon-a-36.png',
  'icon-a-37': '/static/icons/icon-a-37.png',
  'icon-a-38': '/static/icons/icon-a-38.png',
  'icon-a-39': '/static/icons/icon-a-39.png',
  'icon-a-40': '/static/icons/icon-a-40.png',
  'icon-a-41': '/static/icons/icon-a-41.png',
  'icon-a-42': '/static/icons/icon-a-42.png',
  'icon-a-43': '/static/icons/icon-a-43.png',
  'icon-a-44': '/static/icons/icon-a-44.png',
  'icon-a-45': '/static/icons/icon-a-45.png',
  'icon-a-46': '/static/icons/icon-a-46.png',
  'icon-a-47': '/static/icons/icon-a-47.png',
  'icon-a-48': '/static/icons/icon-a-48.png',
  'icon-a-49': '/static/icons/icon-a-49.png',
  'icon-a-50': '/static/icons/icon-a-50.png',
  'icon-a-51': '/static/icons/icon-a-51.png',
  'icon-a-52': '/static/icons/icon-a-52.png',
  'icon-a-53': '/static/icons/icon-a-53.png',
  'icon-a-54': '/static/icons/icon-a-54.png',
  'icon-a-55': '/static/icons/icon-a-55.png',
  'icon-a-56': '/static/icons/icon-a-56.png',
  'icon-a-57': '/static/icons/icon-a-57.png',
  'icon-a-58': '/static/icons/icon-a-58.png',
  'icon-a-59': '/static/icons/icon-a-59.png',
  'icon-a-60': '/static/icons/icon-a-60.png',
  'icon-a-61': '/static/icons/icon-a-61.png',
  'icon-a-62': '/static/icons/icon-a-62.png',
  'icon-a-63': '/static/icons/icon-a-63.png',
  'icon-a-64': '/static/icons/icon-a-64.png',
  'icon-a-65': '/static/icons/icon-a-65.png',
  'icon-a-66': '/static/icons/icon-a-66.png',
  'icon-a-67': '/static/icons/icon-a-67.png',
  'icon-a-68': '/static/icons/icon-a-68.png',
  'icon-a-70': '/static/icons/icon-a-70.png',
  'icon-a-Frame7': '/static/icons/icon-a-Frame7.png',
  'icon-a-Frame8': '/static/icons/icon-a-Frame8.png',
  'icon-addition': '/static/icons/icon-addition.png',
  'icon-biaoqing': '/static/icons/icon-biaoqing.png',
  'icon-chehui': '/static/icons/icon-chehui.png',
  'icon-chuangjianqunzu': '/static/icons/icon-chuangjianqunzu.png',
  'icon-computed': '/static/icons/icon-computed.png',
  'icon-erfenzhiyiyidu': '/static/icons/icon-erfenzhiyiyidu.png',
  'icon-Excel': '/static/icons/icon-Excel.png',
  'icon-fasong': '/static/icons/icon-fasong.png',
  'icon-fuzhi1': '/static/icons/icon-fuzhi1.png',
  'icon-guanbi': '/static/icons/icon-guanbi.png',
  'icon-guanyu': '/static/icons/icon-guanyu.png',
  'icon-huifu': '/static/icons/icon-huifu.png',
  'icon-im-xuanzhong': '/static/icons/icon-im-xuanzhong.png',
  'icon-jiantou': '/static/icons/icon-jiantou.png',
  'icon-jiaruqunzu': '/static/icons/icon-jiaruqunzu.png',
  'icon-kefu': '/static/icons/icon-kefu.png',
  'icon-lahei': '/static/icons/icon-lahei.png',
  'icon-lishixiaoxi': '/static/icons/icon-lishixiaoxi.png',
  'icon-More': '/static/icons/icon-More.png',
  'icon-PPT': '/static/icons/icon-PPT.png',
  'icon-qita': '/static/icons/icon-qita.png',
  'icon-quxiaoxiaoximiandarao': '/static/icons/icon-quxiaoxiaoximiandarao.png',
  'icon-quxiaozhiding': '/static/icons/icon-quxiaoxiaoximiandarao.png',
  'icon-RAR1': '/static/icons/icon-RAR1.png',
  'icon-shanchu': '/static/icons/icon-shanchu.png',
  'icon-shandiao': '/static/icons/icon-shandiao.png',
  'icon-shezhi': '/static/icons/shezhi.png',
  'icon-shezhi1': '/static/icons/icon-shezhi1.png',
  'icon-shipin': '/static/icons/icon-shipin.png',
  'icon-shipin8': '/static/icons/icon-shipin8.png',
  'icon-shipinyuyin': '/static/icons/icon-shipinyuyin.png',
  'icon-sifenzhisanyidu': '/static/icons/icon-sifenzhisanyidu.png',
  'icon-sifenzhiyiyidu': '/static/icons/icon-sifenzhiyiyidu.png',
  'icon-sousuo': '/static/icons/icon-sousuo.png',
  'icon-team': '/static/icons/icon-team.png',
  'icon-zuojiantou': '/static/icons/icon-zuojiantou.png',
  'icon-zhuanfa': '/static/icons/icon-zhuanfa.png',
  'icon-zhongyingwen': '/static/icons/icon-zhongyingwen.png',
  'icon-zhankai': '/static/icons/icon-zhankai.png',
  'icon-yinle': '/static/icons/icon-yinle.png',
  'icon-yidu': '/static/icons/icon-yidu.png',
  'icon-yanzheng': '/static/icons/icon-yanzheng.png',
  'icon-xiaoxizhiding': '/static/icons/icon-xiaoxizhiding.png',
  'icon-xiaoximiandarao': '/static/icons/icon-xiaoximiandarao.png',
  'icon-Word': '/static/icons/icon-Word.png',
  'icon-wenjian': '/static/icons/icon-wenjian.png',
  'icon-weizhiwenjian': '/static/icons/icon-weizhiwenjian.png',
  'icon-weidu': '/static/icons/icon-weidu.png',
  'icon-tupian2': '/static/icons/icon-tupian2.png',
  'icon-tupian1': '/static/icons/icon-tupian1.png',
  'icon-tupian': '/static/icons/icon-tupian.png',
  'icon-tuigejian': '/static/icons/icon-tuigejian.png',
  'icon-tuichudenglu': '/static/icons/icon-tuichudenglu.png',
  'icon-touxiang5': '/static/icons/icon-touxiang5.png',
  'icon-touxiang4': '/static/icons/icon-touxiang4.png',
  'icon-touxiang3': '/static/icons/icon-touxiang3.png',
  'icon-touxiang2': '/static/icons/icon-touxiang2.png',
  'icon-touxiang1': '/static/icons/icon-touxiang1.png',
  'icon-tongxunlu-xuanzhong': '/static/icons/icon-tongxunlu-xuanzhong.png',
  'icon-tongxunlu-weixuanzhong': '/static/icons/icon-tongxunlu-weixuanzhong.png',
  'icon-tianjiahaoyou': '/static/icons/icon-tianjiahaoyou.png',
  'icon-tianjiaanniu': '/static/icons/icon-tianjiaanniu.png',
  'icon-team2': '/static/icons/icon-team2.png',
  'icon-lahei2': '/static/icons/icon-lahei2.png',
  'icon-yuyin1': '/static/icons/icon-yuyin1.png',
  'icon-yuyin2': '/static/icons/icon-yuyin2.png',
  'icon-yuyin3': '/static/icons/icon-yuyin3.png',
  'icon-yuyin8': '/static/icons/icon-yuyin8.png',
  'icon-audio': '/static/icons/audio1.png',
  'audio-btn': '/static/icons/Vector.png', // wu
  'audio-btn-selected': '/static/icons/Frame.png',
  'send-more': '/static/icons/send-more.png', // 无
  'icon-paishe': '/static/icons/paishe.png', // 无
  'icon-shipin2': '/static/icons/icon-shipin2.png',
  'icon-audio-call': '/static/icons/icon-audio-call.png',
  'icon-video-call': '/static/icons/icon-video-call.png',
  'icon-call-mute': '/static/icons/icon-call-mute.png', //无
  'icon-call-mute-active': '/static/icons/icon-call-mute-active.png', //无
  'icon-call-speaker': '/static/icons/icon-call-speaker.png', //无
  'icon-call-speaker-active': '/static/icons/icon-call-speaker-active.png', //无
  'icon-call-switch-camera': '/static/icons/icon-call-switch-camera.png', //无
  'icon-call-hangup': '/static/icons/icon-call-hangup.png', //无
  'icon-bind-identity': '/static/icons/bind-identity.png',
  'icon-keyboard': '/static/icons/keyboard.png',
}

//以上链接访问有频率控制，建议您将静态放到您服务器上，然后修改上面的链接即可

const url = computed(() => {
  // @ts-ignore
  return urlMap[props.type]
})

// const prefix = 'https://yiyong-qa.netease.im/yiyong-static/statics/uniapp-vue2-h5'
// const url = computed(() => {
//   return `${prefix}/static/icons/${props.type}.png`
// })

const className = `${props.iconClassName || ''} icon-wrapper`
</script>

<style scoped lang="scss">
.icon-wrapper {
  display: inline-block;
  line-height: 0;
}

.icon {
  display: inline-block;
  vertical-align: middle;
}
</style>
