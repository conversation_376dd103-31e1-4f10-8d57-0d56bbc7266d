<template>
  <div class="call-confirm-modal" v-if="visible">
    <div class="modal-mask" @tap="handleCancel"></div>
    <div class="modal-content">
      <!-- 图标区域 -->
      <div class="icon-container">
        <div class="call-icon">
          <Icon :type="callType === 1 ? 'icon-audio-call' : 'icon-video-call'" :size="48" color="#337EFF"></Icon>
        </div>
      </div>
      
      <!-- 标题和内容 -->
      <div class="content-area">
        <div class="modal-title">{{ title }}</div>
        <div class="modal-message">{{ message }}</div>
      </div>
      
      <!-- 按钮区域 -->
      <div class="button-area">
        <div class="cancel-btn" @tap="handleCancel">
          <span>{{ cancelText }}</span>
        </div>
        <div class="confirm-btn" @tap="handleConfirm">
          <span>{{ confirmText }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits } from '../../utils/transformVue'
import Icon from './Icon.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: ''
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  callType: {
    type: Number,
    default: 2 // 1: 音频, 2: 视频
  }
})

const emit = defineEmits(['confirm', 'cancel'])

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.call-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  width: 320px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.icon-container {
  padding: 32px 0 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.call-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #337EFF 0%, #4A90E2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(51, 126, 255, 0.3);
}

.content-area {
  padding: 0 24px 32px;
  text-align: center;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12px;
  line-height: 1.4;
}

.modal-message {
  font-size: 16px;
  color: #666666;
  line-height: 1.5;
}

.button-area {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    background-color: #f8f8f8;
  }
}

.cancel-btn {
  color: #666666;
  border-right: 1px solid #f0f0f0;
  
  &:hover {
    background-color: #f8f8f8;
  }
}

.confirm-btn {
  color: #337EFF;
  font-weight: 600;
  
  &:hover {
    background-color: rgba(51, 126, 255, 0.05);
  }
  
  &:active {
    background-color: rgba(51, 126, 255, 0.1);
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .modal-content {
    background: #2a2a2a;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  }
  
  .modal-title {
    color: #ffffff;
  }
  
  .modal-message {
    color: #cccccc;
  }
  
  .button-area {
    border-top-color: #404040;
  }
  
  .cancel-btn {
    color: #cccccc;
    border-right-color: #404040;
    
    &:hover {
      background-color: #404040;
    }
  }
  
  .confirm-btn:hover {
    background-color: rgba(51, 126, 255, 0.15);
  }
}

/* 小屏幕适配 */
@media (max-width: 375px) {
  .modal-content {
    width: 280px;
    margin: 0 20px;
  }
  
  .icon-container {
    padding: 24px 0 20px;
  }
  
  .call-icon {
    width: 64px;
    height: 64px;
  }
  
  .content-area {
    padding: 0 20px 24px;
  }
  
  .modal-title {
    font-size: 18px;
  }
  
  .modal-message {
    font-size: 14px;
  }
  
  .button-area {
    height: 48px;
  }
  
  .cancel-btn,
  .confirm-btn {
    height: 48px;
    font-size: 15px;
  }
}
</style>
