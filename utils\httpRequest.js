import common from "./common.js"
import {
	CLIENT_ID
} from "@/utils/config"

// 添加请求响应拦截处理函数
function handleResponse(result, succ, error, opt) {
	// 判断是否为token过期（401状态码）
	if(result.data.code == 401) {
		// 清除token
		uni.removeStorageSync('token');

		// 尝试执行退出登录操作
		try {
			const app = getApp();
			if (app && app.logout) {
				// 调用应用的logout方法，完成IM退出等操作
				setTimeout(() => {
					app.logout(true);
				}, 100);
			} else {
				// 如果无法获取app实例或logout方法，则使用原来的提示方式
				common.loginTip("登录已过期，请重新登录");
			}
		} catch (e) {
			// 如果出错，回退到原来的提示方式
			console.error('Error during logout:', e);
			common.loginTip("登录已过期，请重新登录");
		}

		// 返回错误
		error.call(self, result.data);
		return false;
	}
	// 其他正常情况，返回成功
	succ.call(self, result.data);
	return true;
}

module.exports = {
	/**
	 * post请求
	 * @param {Object} url [地址]
	 * @param {Object} data [参数]
	 * @param {Object} header [请求头]
	 */
	post: function(opt) {
		let contentType = opt.contentType || "application/json";
		let header = {
			"content-type": contentType,
			'clientId':CLIENT_ID
		}
		let token = uni.getStorageSync('token')
		if(!opt?.noNeedCheck && token){
			header.Authorization = ('Bearer ' + token)
		}

		if(!opt?.noLoad){
			uni.showLoading({title:"",mask:true});
		}
		return new Promise((succ, error) => {
			uni.request({
				url: opt.url,
				data: opt.data,
				method: "POST",
				header: header,
				success: function(result) {
					// 打印请求成功的原始响应
					console.log('请求成功原始响应:', JSON.stringify(result));
					handleResponse(result, succ, error, opt);
				},
				fail: function(e) {
					// 打印请求失败的详细信息
					console.error('请求失败:', JSON.stringify(e));
					console.error('请求URL:', opt.url);
					console.error('请求数据:', JSON.stringify(opt.data));
					error.call(self, e)
				},
				complete: () => {
					if(!opt?.noLoad)
						uni.hideLoading();
				}
			})
		})
	},
	/**
	 * get请求
	 * @param {Object} url [地址]
	 * @param {Object} data [参数]
	 * @param {Object} header [请求头]
	 */
	get: function(opt) {
		let contentType = opt.contentType || "application/x-www-form-urlencoded";
		let header = {
			"content-type": contentType,
			'clientId':CLIENT_ID
		}
		let token = uni.getStorageSync('token')
		if(!opt?.noNeedCheck && token){
			header.Authorization = ('Bearer ' + token)
		}

		if(!opt?.noLoad){
			uni.showLoading({title:"",mask:true});
		}
		return new Promise((succ, error) => {
			uni.request({
				url: opt.url,
				data: opt.data,
				method: "GET",
				header: header,
				success: function(result) {
					handleResponse(result, succ, error, opt);
				},
				fail: function(e) {
					console.log(self);

					error.call(self, e)
				},
				complete: () => {
					if(!opt?.noLoad)
						uni.hideLoading();
				}
			})
		})
	},
	/**
	 * put请求
	 * @param {Object} url [地址]
	 * @param {Object} data [参数]
	 * @param {Object} header [请求头]
	 */
	put: function(opt) {
		let contentType = opt.contentType || "application/json";
		let header = {
			"content-type": contentType,
			'clientId':CLIENT_ID
		}
		let token = uni.getStorageSync('token')
		if(!opt?.noNeedCheck && token){
			header.Authorization = ('Bearer ' + token)
		}

		if(!opt?.noLoad){
			uni.showLoading({title:"",mask:true});
		}
		return new Promise((succ, error) => {
			uni.request({
				url: opt.url,
				data: opt.data,
				method: "PUT",
				header: header,
				success: function(result) {
					handleResponse(result, succ, error, opt);
				},
				fail: function(e) {

					error.call(self, e)
				},
				complete: () => {
					if(!opt?.noLoad)
						uni.hideLoading();
				}
			})
		})
	},
	/**
	 * put请求
	 * @param {Object} url [地址]
	 * @param {Object} data [参数]
	 * @param {Object} header [请求头]
	 */
	delete: function(opt) {
		let contentType = opt.contentType || "application/json";
		let header = {
			"content-type": contentType,
			'clientId':CLIENT_ID
		}
		let token = uni.getStorageSync('token')
		if(!opt?.noNeedCheck && token){
			header.Authorization = ('Bearer ' + token)
		}

		if(!opt?.noLoad){
			uni.showLoading({title:"",mask:true});
		}
		return new Promise((succ, error) => {
			uni.request({
				url: opt.url,
				data: opt.data,
				method: "DELETE",
				header: header,
				success: function(result) {
					handleResponse(result, succ, error, opt);
				},
				fail: function(e) {
					error.call(self, e)
				},
				complete: () => {
					if(!opt?.noLoad)
						uni.hideLoading();
				}
			})
		})
	},
}