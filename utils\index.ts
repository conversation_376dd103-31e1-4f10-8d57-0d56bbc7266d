import { events, STORAGE_KEY } from './constants'

export const getUniPlatform = () => {
  // @ts-ignore
  return uni.getSystemInfoSync().uniPlatform
}

export const getPlatform = () => {
  // @ts-ignore
  return uni.getSystemInfoSync().platform
}
// 是否是android app
export const isAndroidApp =
  uni.getSystemInfoSync().platform == 'android' &&
  uni.getSystemInfoSync().uniPlatform == 'app'

// 是否是Ios app
export const isIosApp =
  uni.getSystemInfoSync().platform == 'ios' &&
  uni.getSystemInfoSync().uniPlatform == 'app'

// 是否是Ios web
export const isIosWeb =
  uni.getSystemInfoSync().uniPlatform === 'web' &&
  uni.getSystemInfoSync().platform === 'ios'

// 是否是App
export const isApp = uni.getSystemInfoSync().uniPlatform == 'app'

// 是否是微信小程序
export const isWxApp = uni.getSystemInfoSync().uniPlatform == 'mp-weixin'

// 是否是web
export const isWeb = uni.getSystemInfoSync().uniPlatform === 'web'

// @ts-ignore
export function deepClone(source: any, visited = new WeakMap()) {
  if (source === null || typeof source !== 'object') {
    return source
  }

  if (visited.has(source)) {
    return visited.get(source)
  }
  // @ts-ignore
  let clone

  if (source instanceof Date) {
    clone = new Date(source.getTime())
  } else if (source instanceof RegExp) {
    clone = new RegExp(source)
  } else if (source instanceof Map) {
    clone = new Map()
    visited.set(source, clone)
    source.forEach((value, key) => {
      // @ts-ignore
      clone.set(key, deepClone(value, visited))
    })
  } else if (source instanceof Set) {
    clone = new Set()
    visited.set(source, clone)
    source.forEach((value) => {
      // @ts-ignore
      clone.add(deepClone(value, visited))
    })
  } else if (Array.isArray(source)) {
    clone = []
    // @ts-ignore
    visited.set(source, clone)
    for (let i = 0; i < source.length; i++) {
      clone[i] = deepClone(source[i], visited)
    }
  } else {
    clone = Object.create(Object.getPrototypeOf(source))
    visited.set(source, clone)
    for (const prop in source) {
      if (source.hasOwnProperty(prop)) {
        clone[prop] = deepClone(source[prop], visited)
      }
    }
  }

  return clone
}

export function stopAllAudio() {
  uni.$emit(events.AUDIO_URL_CHANGE, '')
}

/**
 * 秒转换为时分秒
 */
export const convertSecondsToTime = (seconds: number): string | null => {
  if (!seconds) {
    return null
  }
  const hours: number = Math.floor(seconds / 3600)
  const minutes: number = Math.floor((seconds - hours * 3600) / 60)
  const remainingSeconds: number = seconds - hours * 3600 - minutes * 60

  let timeString = ''

  const includeHours = seconds >= 3600
  if (includeHours) {
    if (hours < 10) {
      timeString += '0'
    }
    timeString += hours.toString() + ':'
  }

  if (minutes < 10) {
    timeString += '0'
  }
  timeString += minutes.toString() + ':'

  if (remainingSeconds < 10) {
    timeString += '0'
  }
  timeString += remainingSeconds.toString()

  return timeString
}


export const startCall = (params: {
  type?: number; // 1: 音频通话, 2: 视频通话
  name?: string; // 远程用户名称
  avatar?: string; // 远程用户头像
  status?: string; // 通话状态
  userId?: string; // 远程用户ID
  callId?: string; // 通话ID
  channelId?: string; // 通话频道ID
  requestId?: string; // 请求ID
} = {}) => {
  // 默认为音频通话
  const callType = params.type || 1;
  // 跳转到1v1Call页面
  uni.navigateTo({
    url: `/pages/Call/1v1Call?type=${callType}&name=${encodeURIComponent(params.name || '')}&avatar=${encodeURIComponent(params.avatar || '')}&status=${params.status || 'calling'}&userId=${params.userId || ''}&channelId=${params.channelId || ''}&callId=${params.callId || ''}&requestId=${params.requestId || ''}`
  });
}

export const startGroupCall = (params: {
  type: number; // 1: 音频通话, 2: 视频通话
  status: string; // 通话状态
  channelId: string; // 群ID，用作房间名
} = {type: 1, status: 'calling', channelId: ''}) => {
  // 默认为音频通话
  const callType = params.type || 1;
  // 跳转到GroupCall页面
  uni.navigateTo({
    url: `/pages/Call/multiCall?type=${params.type}&channelId=${params.channelId}&status=${params.status}`
  });
}
