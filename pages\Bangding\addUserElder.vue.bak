<template>
	<view class="page-container">
		<!-- 标题 -->
		<view class="page-title">
			<text class="title-text">请填写老人信息</text>
			<view class="title-underline"></view>
		</view>

		<!-- 表单内容 -->
		<view class="form-content">
			<!-- 老人信息部分 -->
			<view class="section-title">老人信息</view>

			<view class="form-group">
				<view class="form-row">
					<view class="form-label">老人名字</view>
					<input class="form-input" focus placeholder="请输入" v-model="elderName" />
				</view>
				<view class="form-row">
					<view class="form-label">老人身份证号</view>
					<input class="form-input" placeholder="请输入" v-model="elderIdCard" />
				</view>
			</view>

			<!-- 家属信息部分 -->
			<view class="section-title">家属信息</view>

			<view class="form-group">
				<!-- 关系选择 -->
				<view class="relation-title">您与老人的关系是？</view>
				<view class="relation-options">
					<view class="relation-option" v-for="(item, index) in guanxi" :key="index"
						:class="{ 'relation-option-selected': selectedIndex === index }" @click="selectItem(index)">
						<text>{{ item }}</text>
					</view>
				</view>

				<!-- 家属信息输入 -->
				<view class="form-row">
					<view class="form-label">家属姓名</view>
					<input class="form-input" placeholder="请输入" v-model="kinName" />
				</view>
				<view class="form-row">
					<view class="form-label">家属身份证号</view>
					<input class="form-input" placeholder="请输入" v-model="kinIdCard" />
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-container">
			<button class="submit-button" :loading="istrue" @click="nurseBangdings">提交</button>
		</view>
	</view>
</template>

<script>
	import {
		addUserElder,
		getImUserInfo
	} from "@/api/index"
	// import common from "@/utils/common.js" // 暂时不需要使用
	export default {
		data() {
			return {
				elderName: "",
				elderIdCard: "",
				kinName: "",
				kinIdCard: "",
				kinType: "",
				istrue: false,
				guanxi: ['我的父母', '我的子女', '我的伴侣', '其他'],
				selectedIndex: null,
			}
		},
		methods: {
			// 处理token过期
			handleTokenExpired() {
				uni.showModal({
					title: "登录已过期，请重新登录",
					confirmText: "前往",
					cancelText: "稍后",
					confirmColor: "#07c160",
					success: res => {
						if (res.confirm) {
							// 执行退出登录操作
							const app = getApp();
							if (app && app.logout) {
								// 调用应用的logout方法，完成IM退出等操作
								app.logout();
							} else {
								// 如果无法获取app实例或logout方法，则直接跳转到登录页面
								uni.navigateTo({
									url: "/pages/Login/index"
								});
							}
						}
					}
				});
			},

			nurseBangdings() {
				let nurseInfo = {
					elderName: this.elderName,
					elderIdCard: this.elderIdCard,
					kinName: this.kinName,
					kinIdCard: this.kinIdCard,
					kinType: this.kinType,
				}
				addUserElder(nurseInfo).then(res => {
					// 处理token过期情况
					if (res.code == 401) {
						// 清除token
						uni.removeStorageSync('token');
						// 显示token过期弹窗
						this.handleTokenExpired();
					} else if (res.code == 200) {
						uni.showToast({
							title: res.msg,
							icon: 'success',
							duration: 2000
						});
						getImUserInfo().then(data => {
							uni.setStorageSync('userInfo',data.data)
							setTimeout(()=>{
								uni.switchTab({url:"/pages/Conversation/index"})
							},2000)
						})
					} else {
						uni.showToast({
							title: res.msg || '信息填写错误，请检查',
							icon: 'none',
							duration: 2000
						});
					}

				}).catch(error => {
					// 检查是否是token过期
					if (error && error.code == 401) {
						// 清除token
						uni.removeStorageSync('token');
						// 显示token过期弹窗
						this.handleTokenExpired();
					} else {
						uni.showToast({
							title: '服务器异常',
							icon: 'none',
							duration: 2000
						});
					}
				})
			},
			selectItem(index) {
				this.selectedIndex = index;
				this.kinType = index;
			},
		}
	}
</script>

<style lang="scss">
/* 使用相对单位和响应式设计 */
.page-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 0 4%;
  box-sizing: border-box;
}

.page-title {
  text-align: center;
  padding: 4% 0 2%;

  .title-text {
    font-size: 1rem;
    font-weight: 500;
    color: #00a57a;
  }

  .title-underline {
    width: 40px;
    height: 2px;
    background-color: #00a57a;
    margin: 0.4rem auto 0;
  }
}

.form-content {
  width: 100%;
  max-width: 800px; /* 平板上限制最大宽度 */
  margin: 0 auto;
}

.section-title {
  font-size: 0.95rem;
  font-weight: 500;
  color: #333;
  margin: 1rem 0 0.6rem;
  padding-bottom: 0.3rem;
  border-bottom: 1px solid #f0f0f0;
}

.form-group {
  margin-bottom: 0.8rem;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.8rem;

  .form-label {
    width: 35%;
    font-size: 0.9rem;
    color: #333;
    font-weight: 400;
    padding-right: 0.5rem;
    box-sizing: border-box;
  }

  .form-input {
    flex: 1;
    height: 2.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 0 0.8rem;
    font-size: 0.9rem;
    color: #333;
    background-color: #fff;
    box-sizing: border-box;

    &:focus {
      border-color: #00a57a;
      outline: none;
    }
  }
}

.relation-title {
  font-size: 0.9rem;
  font-weight: 400;
  color: #333;
  margin-bottom: 0.6rem;
}

.relation-options {
  display: flex;
  flex-wrap: wrap;
  gap: 2%;
  margin-bottom: 1rem;

  .relation-option {
    flex: 1;
    min-width: 22%;
    height: 2.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 0.5rem;

    text {
      font-size: 0.9rem;
      color: #666;
    }

    &:active {
      background-color: #f0f0f0;
    }

    &.relation-option-selected {
      background-color: #f0f0f0;

      text {
        color: #333;
        font-weight: 500;
      }
    }
  }
}

.submit-container {
  width: 100%;
  max-width: 800px;
  margin: 1.5rem auto 0;
  padding-bottom: 1.5rem;

  .submit-button {
    width: 100%;
    height: 2.8rem;
    background-color: #00a57a;
    border-radius: 1.4rem;
    color: white;
    font-size: 1rem;
    font-weight: 500;
    border: none;

    &:active {
      opacity: 0.9;
    }
  }
}

/* 适配小屏幕手机 */
@media screen and (max-width: 360px) {
  .page-container {
    padding: 0 3%;
  }

  .page-title .title-text {
    font-size: 0.95rem;
  }

  .section-title {
    font-size: 0.9rem;
  }

  .form-row {
    margin-bottom: 0.7rem;

    .form-label {
      font-size: 0.85rem;
    }

    .form-input {
      height: 2.3rem;
      font-size: 0.85rem;
      padding: 0 0.6rem;
    }
  }

  .relation-title {
    font-size: 0.85rem;
  }

  .relation-options {
    gap: 1.5%;

    .relation-option {
      height: 2.1rem;

      text {
        font-size: 0.85rem;
      }
    }
  }

  .submit-button {
    height: 2.5rem;
    font-size: 0.95rem;
  }
}

/* 适配平板设备 */
@media screen and (min-width: 768px) {
  .page-container {
    padding: 0 6%;
  }

  .page-title {
    padding: 2% 0;

    .title-text {
      font-size: 1.2rem;
    }

    .title-underline {
      width: 50px;
      margin-top: 0.5rem;
    }
  }

  .form-content {
    padding: 0 2%;
  }

  .section-title {
    font-size: 1.1rem;
    margin: 1.5rem 0 1rem;
  }

  .form-row {
    margin-bottom: 1.2rem;

    .form-label {
      font-size: 1rem;
      width: 30%;
    }

    .form-input {
      height: 2.8rem;
      font-size: 1rem;
      padding: 0 1rem;
    }
  }

  .relation-title {
    font-size: 1rem;
    margin-bottom: 0.8rem;
  }

  .relation-options {
    gap: 2.5%;

    .relation-option {
      height: 2.6rem;
      min-width: 20%;

      text {
        font-size: 1rem;
      }
    }
  }

  .submit-container {
    margin-top: 2rem;

    .submit-button {
      width: 60%;
      height: 3rem;
      margin: 0 auto;
      font-size: 1.1rem;
    }
  }
}
</style>