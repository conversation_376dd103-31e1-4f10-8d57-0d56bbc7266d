<template>
	<div class="discover_page">
		<div class="navigation-bar">
			<div class="logo-box">
				<div>照顾计划</div>
			</div>
		</div>
		<div class="no-identity-tip" v-if="!chatIdentity">
            <div class="tip-icon">
                <i class="iconfont icon-bangding"></i>
            </div>
            <div class="tip-text">您尚未绑定身份，请先绑定身份</div>
            <div class="bind-btn" @click="gotoBangding">立即绑定</div>
        </div>
		<ul class="discover_page_ul" v-else>
			<li v-for="(item, index) in staticList" v-show="(chatIdentity == item.chatIdentity) || !item.chatIdentity"
				:key="index" @click="goPage(item)">{{ item.name }}</li>
		</ul>
	</div>
</template>

<script>
import { customNavigateTo } from '../../utils/customNavigate'
import { t } from '../../utils/i18n'
export default {
	data() {
		return {
			chatIdentity: uni.getStorageSync("x_chat_user_info").appUser.chatIdentity || '',
			staticList: [
				{
					name: '护理员照护计划',
					path: '/pages/Discover/carePlan/index',
					chatIdentity: '1'
				},
				{
					name: '长者照护计划',
					path: '/pages/Discover/elder/index',
					chatIdentity: '0'
				},
			]
		};
	},
	onShow() {
		// 每次显示页面时更新身份信息
		if (!this.chatIdentity) {
			console.log("更新身份");
			const userInfo = uni.getStorageSync("x_chat_user_info")
			if (userInfo && userInfo.appUser) {
				this.chatIdentity = userInfo.appUser.chatIdentity || ''
			}
		}
	},

	methods: {
		goPage(item) {
			customNavigateTo({
				url: item.path,
			})
		},
		gotoBangding() {
            customNavigateTo({
                url: '/pages/Bangding/index',
            })
        }
	},
}
</script>

<style lang="scss">
.discover_page {
	.navigation-bar {
		height: 60px;
		border-bottom: 1rpx solid #e9eff5;
		padding: 0 20px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-top: var(--status-bar-height);

		.logo-box {
			display: flex;
			align-items: center;
			font-size: 20px;
			font-weight: 500;
		}
	}

	.discover_page_ul {
		padding: 0 20px;
		list-style: none;

		li {
			height: 60px;
			line-height: 60px;
			border-bottom: 1px solid #f2f2f2;
			font-size: 16px;
			color: #333;
			position: relative;

			&::after {
				content: "";
				height: 8px;
				width: 8px;
				top: 50%;
				right: 20px;
				margin-top: -5px;
				border-width: 2px 2px 0 0;
				border-color: #999;
				border-style: solid;
				transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
				position: absolute;
			}

		}
	}
	.no-identity-tip {
        margin-top: 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0 20px;
        
        .tip-icon {
            margin-bottom: 20px;
            color: #999;
            font-size: 40px;
        }
        
        .tip-text {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .bind-btn {
            padding: 10px 30px;
            background-color: #337EFF;
            color: #fff;
            border-radius: 20px;
            font-size: 14px;
        }
    }
}
</style>