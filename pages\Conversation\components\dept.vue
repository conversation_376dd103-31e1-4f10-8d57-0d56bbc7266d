<template>
	<view class="dept-list">
		<view class="title">绑定用户所属公司</view>
		<scroll-view class="content" scroll-y>
			<view v-for="item in deptList" :key="item.deptId" class="dept-item" @click="checkDept(item.deptId)">
				<checkbox class="round" :checked="checkDeptId == item.deptId" 
					style="transform: scale(.7);margin-top: -8rpx;" />
				<view class="name">
					{{item.deptName}}
				</view>
			</view>
		</scroll-view>
		<view class="btn-list">
			<view class="sure-btn" @click="bindDept">
				确认
			</view>
		</view>
	</view>
</template>

<script>
	import { updateUser } from "@/api/index"
	import {
		CLIENT_ID
	} from "@/utils/config"
	export default {
		props:{
			deptList:{
				type:Array,
				default:()=>[]
			}
		},
		data(){
			return {
				checkDeptId:null
			}
		},
		methods:{
			checkDept(id){
				if(this.checkDeptId == id){
					this.checkDeptId = null
				}else{
					this.checkDeptId = id;
				}
				console.log("选中部门",this.checkDeptId);
			},
			bindDept(){
				if(!this.checkDeptId){
					this.$common.tipMsg('请选择所属分公司')
					return
				}
				updateUser({deptId:this.checkDeptId}).then(res=>{
					if(res?.code == 200){
						const userInfo = uni.getStorageSync('userInfo')
						userInfo.deptId = this.checkDeptId
						uni.setStorageSync('userInfo',userInfo)
						this.$common.tipMsg('绑定成功')
						this.$emit('close')
					}else{
						const msg = res?.msg || this.$common.errmsg
						this.$common.tipMsg(msg)
						this.$emit('close')
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.dept-list{
		height: 100%;
		.title{
			width:100%;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $uni-color-primary;
			font-size: 40rpx;
		}
		.content{
			height: calc(100% - 80rpx - 120rpx);
			.dept-item{
				display: flex;
				align-items: center;
				padding: 0 20rpx;
				height: 60rpx;
				.round{
					border-radius: 50%;
					height: 30rpx;
					width: 30rpx;
				}
				.name{
					margin-left: 30rpx;
					font-size: 34rpx;
				}
			}
		}
		.btn-list{
			display: flex;
			align-items: center;
			justify-content: center;
			height: 80rpx;
			margin-top: 20rpx;
			margin-bottom: 20rpx;
			.sure-btn{
				width: 80%;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				color: #fff;
				background-color: $uni-color-primary;
				border-radius: 40rpx;
			}
		}
	}
	
</style>