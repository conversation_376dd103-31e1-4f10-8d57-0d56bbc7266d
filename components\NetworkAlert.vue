<!--
 * @Author: 肖剑峰 <EMAIL>
 * @Date: 2025-03-11 22:40:44
 * @LastEditors: 肖剑峰 <EMAIL>
 * @LastEditTime: 2025-03-12 08:49:40
 * @FilePath: \hospital-UIh:\KangYang_work\Acare-xchat\components\NetworkAlert.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div
    v-if="showAlert" 
    class="network-alert" 
    :class="{ 'network-alert-offline': !isConnected }"
    @click="handleReconnect">
    {{ text }} <span v-if="!isConnected" class="retry-text">点击重试</span>
  </div>
</template>

<script lang="ts" setup>
import { autorun } from 'mobx'
import { ref, onMounted, onUnmounted } from '../utils/transformVue'
import { refreshToken } from '../api/index.js'
import { t } from '../utils/i18n'
import { STORAGE_KEY } from 'utils/constants'
const isConnected = ref(true)
const text = ref(t('connectingText'))
const showAlert = ref(false) // 新增控制提示显示的状态
// uni.onNetworkStatusChange((res) => {
//   if (!res.isConnected) {
//     isConnected.value = false;
//     text.value = t('offlineText');
//   } else {
//     text.value = t('connectingText');
//   }
// });

onMounted(() => {
  // @ts-ignore
  if (uni.$UIKitStore?.connectStore?.connectState === 'connected') {
    isConnected.value = true
    showAlert.value = false
    // @ts-ignore
  } else if (uni.$UIKitStore?.connectStore?.connectState === 'disconnected') {
    isConnected.value = false
    text.value = t('offlineText')
    showAlert.value = true
  } else {
    isConnected.value = false
    text.value = t('connectingText')
    setTimeout(() => {
      if (!isConnected.value) {
        showAlert.value = true
      }
    }, 3000)
  }
})

// 添加日志函数
const logConnectionState = (state) => {
  console.log('IM连接状态:', state)
}

const uninstallConnectWatch = autorun(() => {
  // @ts-ignore
  const connectState = uni.$UIKitStore?.connectStore?.connectState
  logConnectionState(connectState)

  if (connectState === 'connected') {
    isConnected.value = true
    // @ts-ignore
  } else if (connectState === 'disconnected') {
    isConnected.value = false
    text.value = t('offlineText')
  } else {
    isConnected.value = false
    text.value = t('connectingText')
  }
})

onUnmounted(() => {
  uninstallConnectWatch()
})

// 添加手动重连方法

const reconnectCount = ref(0) // 添加重连次数计数器
const handleReconnect = () => {
  text.value = t('connectingText')
  reconnectCount.value += 1 // 每次点击增加计数
  // @ts-ignore
  if (uni.$UIKitStore?.connectStore?.reconnect) {
    console.log('如果有重连API，调用它')
    // @ts-ignore
    uni.$UIKitStore.connectStore.reconnect()
  } else {
    // 前两次尝试直接连接
    if (reconnectCount.value <= 2) {
      console.log(`第${reconnectCount.value}次尝试直接连接`)
      // @ts-ignore
      // 尝试重新初始化连接
      uni.$im?.connect && uni.$im.connect()
    } else {
      const userInfo = uni.getStorageSync('userInfo')
      const accId = userInfo?.accid

      refreshToken(accId)
      // @ts-ignore
        .then((res) => {
          if (res.code === 200 && res.data) {
            // 获取 App 实例
            const app = getApp()
            // 保存新的 token 到本地存储
            const imOptions = {
              account: accId,
              token: res.data.info.token,
            }
            // 保存到 IM 存储
            uni.setStorage({
              key: STORAGE_KEY, // 需要使用正确的 STORAGE_KEY 常量
              data: imOptions,
              success: function () {
                console.log('保存IM登录信息成功')
                // 使用新的 token 重新初始化 IM 连接
                app.initNim(imOptions)
                // 重置重连计数
                reconnectCount.value = 0
                // 应用重新
                uni.reLaunch({
                  url: '/pages/Conversation/index',
                  success: () => {
                    console.log('应用重新加载成功')
                  }
                })
              },
            })
          } else {
            // 刷新 token 失败
            text.value =
              t('refreshTokenFailedText') || '刷新连接失败，请稍后再试'
          }
        })
        .catch((err:Error) => {
          console.error('刷新 token 失败', err)
          text.value = t('refreshTokenFailedText') || '刷新连接失败，请稍后再试'
        })
    }
  }
}
</script>

<style>
.network-alert {
  font-size: 14px;
  background: #fee3e6;
  color: #fc596a;
  text-align: center;
  padding: 8px 0;
  cursor: pointer;
}
.retry-text {
  text-decoration: underline;
  margin-left: 5px;
}
</style>
