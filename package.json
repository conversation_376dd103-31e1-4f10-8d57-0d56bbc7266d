{"name": "vue2-uniapp-ne", "private": true, "version": "1.0.0", "description": "`#my：`为自己拓展代码", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@dcloudio/uni-app": "^3.0.0-3070320230222002", "@dcloudio/uni-ui": "^1.5.7", "@vue/composition-api": "^1.7.2", "@xkit-yx/core-kit": "^0.13.0", "@xkit-yx/im-store": "^0.3.0", "@xkit-yx/utils": "^0.5.6", "axios": "0.27.2", "dayjs": "^1.11.0", "mobx": "^6.11.0", "pinyin": "^3.1.0", "segmentit": "^2.0.3", "sm-crypto": "^0.3.13", "vant": "2.12"}, "devDependencies": {"babel-plugin-import": "^1.13.8", "sass": "^1.83.0", "unplugin-vue2-script-setup": "^0.11.4"}, "repository": {"type": "git", "url": "https://codeup.aliyun.com/65902eb5e7f9ce3ec819a205/Acare-xchat.git"}}