<template>
	<view class="bangding_body">
		<view class="background_img">
			<image src="../../static/bangding/bgimg.png" mode="widthFix"></image>
		</view>
		<view class="content-container">
			<uni-card class="card-wrapper">
				<view class="title">
					选择身份
				</view>
				<view class="chiose">
					<view class="identity-option" @click="gotoNurse">
						<view class="msg-image">
							<image src="../../static/bangding/nurse.png" mode="aspectFit"></image>
						</view>
						<view class="identity-text">
							<view class="identity-title">我是护理员</view>
							<view class="identity-desc">选择绑定护理员身份</view>
						</view>
					</view>
					<view class="identity-option" @click="gotoElder">
						<view class="identity-text">
							<view class="identity-title">我是家属</view>
							<view class="identity-desc">选择绑定家属身份</view>
						</view>
						<view class="msg-image">
							<image src="../../static/bangding/elder.jpg" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</uni-card>
		</view>
	</view>
</template>

<script>

	export default {

		data() {
			return {

			}
		},
		mounted() {

		},
		methods: {
			gotoNurse() {
				// 检查是否已绑定身份
				if (this.$common.checkIdentityBound()) {
					return;
				}
				uni.navigateTo({
					url: "/pages/Bangding/addUserNurse"
				})
			},
			gotoElder() {
				if (this.$common.checkIdentityBound()) {
					return;
				}
				uni.navigateTo({
					url: "/pages/Bangding/addUserElder"
				})
			},

		}
	}
</script>

<style lang="less">
	.bangding_body {
		position: relative;
		width: 100%;
		height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
	}

	.background_img {
		width: 100%;
		background-color: #4a90e2;
		overflow: hidden;

		image {
			width: 100%;
			display: block;
		}
	}

	.content-container {
		position: relative;
		width: 100%;
		display: flex;
		justify-content: center;
		margin-top: -50px;
		padding: 0 20px;
		box-sizing: border-box;
	}

	.card-wrapper {
		width: 100%;
		max-width: 500px;
		border-radius: 15px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		background-color: #fff;
		overflow: hidden;

		.title {
			width: 100%;
			padding: 20px 0;
			text-align: center;
			font-size: 20px;
			font-weight: 600;
			color: #333333;
		}

		.chiose {
			padding: 10px 15px 20px;

			.identity-option {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 15px;
				margin-bottom: 15px;
				border-radius: 10px;
				background-color: #f9f9f9;
				transition: all 0.3s ease;

				&:active {
					background-color: #eaeaea;
				}

				.msg-image {
					width: 80px;
					height: 80px;
					overflow: hidden;
					border-radius: 8px;

					image {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
				}

				.identity-text {
					flex: 1;
					padding: 0 15px;

					.identity-title {
						font-size: 18px;
						font-weight: 500;
						color: #333;
						margin-bottom: 5px;
					}

					.identity-desc {
						font-size: 14px;
						color: #666;
					}
				}
			}
		}
	}

	/* 适配小屏幕手机 */
	@media screen and (max-width: 320px) {
		.card-wrapper {
			.chiose {
				.identity-option {
					.msg-image {
						width: 60px;
						height: 60px;
					}

					.identity-text {
						.identity-title {
							font-size: 16px;
						}

						.identity-desc {
							font-size: 12px;
						}
					}
				}
			}
		}
	}
</style>