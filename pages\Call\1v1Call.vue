<template>
  <div class="call-page">
    <!-- 顶部状态栏 -->
    <div class="call-status-bar">
      <div class="call-status">{{ callStatusText }}</div>
      <div class="call-duration" v-if="callStatus === 'connected'">{{ formattedDuration }}</div>
    </div>

    <!-- 用户信息区域 (仅在非视频通话或未接通时显示) -->
    <div class="call-user-info" v-if="callType !== 2 || callStatus !== 'connected'">
      <div class="call-avatar">
        <Avatar :account="remoteUserId" :avatar="remoteUserAvatar" size="100" />
      </div>
      <div class="call-username">{{ remoteUserName }}</div>
    </div>

    <!-- 视频区域 (仅在视频通话时显示) -->
    <div class="call-video-container" v-if="callType === 2">
      <!-- 远程视频 -->
      <div class="remote-video-view">
        <!-- #ifdef APP-PLUS -->
        <NertcRemoteView :userID="remoteUserId" mediaType="video" @onViewLoad="handleRemoteViewLoad" style="height: 100%; width: 100%;" />
        <!-- #endif -->
      </div>
      <!-- 本地视频 (小窗口) -->
      <div class="local-video-view">
        <!-- #ifdef APP-PLUS -->
        <NertcLocalView :viewID="imOptions.accid" mediaType="video" @onViewLoad="handleLocalViewLoad" style="height: 100%; width: 100%; border: 2px solid rgba(255, 255, 255, 0.5);" />
        <!-- #endif -->
      </div>
    </div>

    <!-- 通话控制按钮 -->
    <div class="call-controls">
      <!-- 控制按钮行（始终显示） -->
      <div class="call-action-row">
        <!-- 静音按钮 -->
        <div class="call-action-btn" :class="{ 'active-btn': isMuted }" @tap="handleToggleMute">
          <Icon :type="isMuted ? 'icon-call-mute-active' : 'icon-call-mute'" :size="24"></Icon>
          <div class="btn-text">{{ t('muteText') }}</div>
        </div>

        <!-- 扬声器按钮 -->
        <div class="call-action-btn" :class="{ 'active-btn': isSpeakerOn }" @tap="handleToggleSpeaker">
          <Icon :type="isSpeakerOn ? 'icon-call-speaker-active' : 'icon-call-speaker'" :size="24"></Icon>
          <div class="btn-text">{{ t('speakerText') }}</div>
        </div>

        <!-- 切换摄像头按钮 (仅视频通话时显示) -->
        <div class="call-action-btn" v-if="callType === 2" @tap="handleSwitchCamera">
          <Icon type="icon-call-switch-camera" :size="24"></Icon>
          <div class="btn-text">{{ t('switchCameraText') }}</div>
        </div>
      </div>

      <!-- 未接通时的按钮 -->
      <template v-if="callStatus !== 'connected'">
        <!-- 主叫方按钮 -->
        <div v-if="isCallInitiator" class="call-action-row">
          <div class="call-action-btn hangup-btn" @tap="handleHangup">
            <Icon type="icon-audio-call" :size="24"></Icon>
          </div>
        </div>

        <!-- 被叫方按钮 -->
        <div v-else class="call-action-row">
          <div class="call-action-btn decline-btn" @tap="handleDecline">
            <Icon type="icon-call-hangup" :size="30" style="margin: auto;"></Icon>
          </div>
          <div class="call-action-btn accept-btn" @tap="handleAccept">
            <Icon :type="callType === 1 ? 'icon-audio-call' : 'icon-video-call'" :size="30" style="margin: auto;"></Icon>
          </div>
        </div>
      </template>

      <!-- 已接通时的挂断按钮 -->
      <template v-else>
        <div class="call-action-row">
          <div class="call-action-btn hangup-btn" @tap="handleHangup">
            <Icon type="icon-call-hangup" :size="30" style="margin: auto;"></Icon>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from '../../utils/transformVue'
import Icon from '../../components/Icon.vue'
import Avatar from '../../components/Avatar.vue'
import { t } from '../../utils/i18n'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { STORAGE_KEY } from 'utils/constants'
import Config from '../../utils/config'
// #ifdef APP-PLUS
import NertcLocalView from "../../NERtcUniappSDK-JS/nertc-view/NertcLocalView";
import NertcRemoteView from "../../NERtcUniappSDK-JS/nertc-view/NertcRemoteView";
// #endif
import callService from '../../services/CallService';
import { getImToken, getPermissionKey } from '../../api/resource';
// 通话类型：1-音频通话，2-视频通话
const callType = ref(1)
// 通话状态：calling-呼叫中，ringing-响铃中，connected-已接通，ended-已结束
const callStatus = ref('calling')
// 远程用户信息
const remoteUserName = ref('用户名')
const remoteUserAvatar = ref('')
const remoteUserId = ref('')
const defaultAvatar = '/static/images/default-avatar.png'
// 是否为主叫方（发起通话的一方）
const isCallInitiator = ref(true)

// 通话控制状态
const isMuted = ref(false)
const isSpeakerOn = ref(false)
const callStartTime = ref(0)
const callDuration = ref(0)
let timerInterval: any = null
let callId = ref('')
let requestId = ref('')
let channelId = ref('')
const imOptions = uni.getStorageSync(STORAGE_KEY)
// 是否已初始化引擎
const isSetup = ref(false) //是否初始化引擎
// 计算属性：通话状态文本
const callStatusText = computed(() => {
  switch (callStatus.value) {
    case 'calling':
      return callType.value === 1 ? t('audioCallingText') : t('videoCallingText')
    case 'ringing':
      return t('ringingText')
    case 'connected':
      return callType.value === 1 ? t('audioConnectedText') : t('videoConnectedText')
    case 'ended':
      return t('callEndedText')
    default:
      return ''
  }
})

// 计算属性：格式化通话时长
const formattedDuration = computed(() => {
  const seconds = callDuration.value
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60

  const formattedMinutes = minutes < 10 ? `0${minutes}` : `${minutes}`
  const formattedSeconds = remainingSeconds < 10 ? `0${remainingSeconds}` : `${remainingSeconds}`

  return `${formattedMinutes}:${formattedSeconds}`
})

// 处理接听通话
const handleAccept = async () => {
  if (channelId.value && remoteUserId.value) {
    try {
      // @ts-ignore
      const nim = uni.$UIKitNIM?.getNIM();
      if (!nim) {
        console.error('NIM未初始化');
        uni.showToast({
          title: 'IM服务未初始化',
          icon: 'none'
        });
        return;
      }

      // 使用V2NIMSignallingService接受通话
      const params = {
        callerAccountId: remoteUserId.value,
        requestId: requestId.value,
        channelId: channelId.value,
        serverExtension: '',
        signallingConfig: {
          unreadEnabled: true,
          offlineEnabled: true,
          selfUid: imOptions.accid
        },
        rtcConfig: {
          rtcChannelName: channelId.value,
          rtcTokenTtl: 3600,
          rtcParams: ''
        }
      };

      // @ts-ignore
      const result = await nim.V2NIMSignallingService.callSetup(params);
      console.log('接受通话成功:', result);

      // 接受通话后加入频道
      callStatus.value = 'connected';
      startCallTimer();

      // 如果是视频通话，确保引擎已初始化
      if (callType.value === 2 && !isSetup.value) {
        initCallEngine();
      }

      // 加入通话频道
      joinChannel();
    } catch (error) {
      console.error('接听通话失败:', error);
      uni.showToast({
        title: t('callFailedText'),
        icon: 'none'
      });
    }
  } else {
    // 如果没有频道ID和远程用户ID，可能是测试模式
    callStatus.value = 'connected';
    startCallTimer();

    // 如果是视频通话，确保引擎已初始化
    if (callType.value === 2 && !isSetup.value) {
      initCallEngine();
    }

    // 加入通话频道
    joinChannel();
  }
}

// 处理拒绝通话
const handleDecline = async () => {
  if (channelId.value && remoteUserId.value) {
    try {
      // @ts-ignore
      const nim = uni.$UIKitNIM?.getNIM();
      if (!nim) {
        console.error('NIM未初始化');
        uni.showToast({
          title: 'IM服务未初始化',
          icon: 'none'
        });
        return;
      }

      // 使用V2NIMSignallingService拒绝通话
      const params = {
        channelId: channelId.value,
        accountId: remoteUserId.value,
        requestId: requestId.value,
        customInfo: JSON.stringify({ reason: 'user_reject' })
      };

      // @ts-ignore
      await nim.V2NIMSignallingService.reject(params);
      console.log('拒绝通话成功');
    } catch (error) {
      console.error('拒绝通话失败:', error);
      uni.showToast({
        title: '拒绝通话失败，但将结束通话',
        icon: 'none'
      });
    }
  }

  // 无论信令发送是否成功，都结束通话
  callStatus.value = 'ended';

  // 如果引擎已初始化，离开频道
  if (isSetup.value) {
    try {
      callService.leaveChannel();
    } catch (error) {
      console.error('离开频道失败:', error);
    }
  }

  setTimeout(() => {
    uni.navigateBack();
  }, 1000);
}

// 处理挂断通话
const handleHangup = async () => {
  if (channelId.value) {
    try {
      // @ts-ignore
      const nim = uni.$UIKitNIM?.getNIM();
      if (!nim) {
        console.error('NIM未初始化');
        uni.showToast({
          title: 'IM服务未初始化',
          icon: 'none'
        });
        // 即使信令服务不可用，仍然继续挂断通话流程
      } else {
        // 使用V2NIMSignallingService挂断通话
        const params = {
          channelId: channelId.value,
          offlineEnabled: true
        };

        try {
          // @ts-ignore
          await nim.V2NIMSignallingService.close(params);
          console.log('挂断通话成功');
        } catch (error) {
          console.error('挂断通话失败:', error);
          uni.showToast({
            title: '信令服务挂断失败，但将结束通话',
            icon: 'none'
          });
        }
      }
    } catch (error) {
      console.error('获取NIM实例失败:', error);
    }
  }

  // 离开音视频频道
  if (isSetup.value) {
    try {
      // 如果是视频通话，停止预览
      if (callType.value === 2) {
        callService.stopPreview();
      }
      callService.leaveChannel();
      console.log('离开频道成功');
    } catch (error) {
      console.error('离开频道失败:', error);
    }
  }

  callStatus.value = 'ended';
  stopCallTimer();
  setTimeout(() => {
    uni.navigateBack();
  }, 1000);
}

// 处理静音切换
const handleToggleMute = () => {
  // 切换静音状态
  isMuted.value = !isMuted.value

  // 如果引擎已初始化，调用API设置静音
  if (isSetup.value) {
    try {
      callService.muteLocalAudio(isMuted.value);
      console.log(`${isMuted.value ? '开启' : '关闭'}静音`);
    } catch (error) {
      console.error('切换静音失败:', error);
      // 恢复状态
      isMuted.value = !isMuted.value;
      uni.showToast({
        title: '切换静音失败',
        icon: 'none'
      });
      return;
    }
  }

  // 显示提示
  uni.showToast({
    title: isMuted.value ? '已静音' : '已取消静音',
    icon: 'none'
  });
}

// 处理扬声器切换
const handleToggleSpeaker = () => {
  // 切换扬声器状态
  isSpeakerOn.value = !isSpeakerOn.value

  // 如果引擎已初始化，调用API设置扬声器
  if (isSetup.value) {
    try {
      callService.setSpeakerphoneOn(isSpeakerOn.value);
      console.log(`${isSpeakerOn.value ? '开启' : '关闭'}扬声器`);
    } catch (error) {
      console.error('切换扬声器失败:', error);
      // 恢复状态
      isSpeakerOn.value = !isSpeakerOn.value;
      uni.showToast({
        title: '切换扬声器失败',
        icon: 'none'
      });
      return;
    }
  }

  // 显示提示
  uni.showToast({
    title: isSpeakerOn.value ? '已切换到扬声器' : '已切换到听筒',
    icon: 'none'
  });
}

// 处理摄像头切换
const handleSwitchCamera = () => {
  // 如果不是视频通话，不允许切换摄像头
  if (callType.value !== 2) {
    uni.showToast({
      title: '仅视频通话可切换摄像头',
      icon: 'none'
    });
    return;
  }

  // 如果引擎已初始化，调用API切换摄像头
  if (isSetup.value) {
    try {
      callService.switchCamera();
      console.log('切换摄像头成功');
      uni.showToast({
        title: t('cameraSwitchedText'),
        icon: 'none'
      });
    } catch (error) {
      console.error('切换摄像头失败:', error);
      uni.showToast({
        title: '切换摄像头失败',
        icon: 'none'
      });
    }
  } else {
    // 引擎未初始化，但仍然显示切换成功提示
    console.log('引擎未初始化，但显示切换成功');
    uni.showToast({
      title: t('cameraSwitchedText'),
      icon: 'none'
    });
  }
}

// 开始计时器
const startCallTimer = () => {
  callStartTime.value = Math.floor(Date.now() / 1000)
  timerInterval = setInterval(() => {
    const now = Math.floor(Date.now() / 1000)
    callDuration.value = now - callStartTime.value
  }, 1000)
}

// 停止计时器
const stopCallTimer = () => {
  if (timerInterval) {
    clearInterval(timerInterval)
    timerInterval = null
  }
}

// 初始化通话引擎
const initCallEngine = () => {
  if (isSetup.value) return; // 避免重复初始化

  // 使用callService初始化引擎
  callService.setupEngine();

  // 注册事件监听
  callService.addEventListener();

  // 设置本地视频画布 - 只在初始化时设置基本参数，具体view在handleLocalViewLoad中设置
  callService.setupLocalVideoCanvas();

  // 请求权限
  callService.requestPermissions();

  // 启用本地视频
  if (callType.value === 2) { // 仅在视频通话时启用
    callService.enableLocalVideo(true);
  }

  // 设置默认扬声器状态
  // 视频通话默认开启扬声器，音频通话默认关闭
  isSpeakerOn.value = callType.value === 2;
  try {
    callService.setSpeakerphoneOn(isSpeakerOn.value);
    console.log(`初始化${isSpeakerOn.value ? '开启' : '关闭'}扬声器`);
  } catch (error) {
    console.error('设置扬声器初始状态失败:', error);
  }

  // 默认不静音
  isMuted.value = false;
  try {
    callService.muteLocalAudio(false);
  } catch (error) {
    console.error('设置静音初始状态失败:', error);
  }

  // 注册自定义事件监听
  registerCallServiceEvents();

  isSetup.value = true;
}

// 注册callService事件监听
const registerCallServiceEvents = () => {
  // 加入频道事件
  callService.on('joinChannel', (data) => {
    if (data.result === 0) {
      callStatus.value = 'connected';
      startCallTimer();

      // 连接成功后再次确认扬声器状态
      try {
        // 视频通话默认开启扬声器，音频通话默认关闭
        isSpeakerOn.value = callType.value === 2;
        callService.setSpeakerphoneOn(isSpeakerOn.value);
        console.log(`连接后${isSpeakerOn.value ? '开启' : '关闭'}扬声器`);
      } catch (error) {
        console.error('连接后设置扬声器状态失败:', error);
      }
    }
  });

  // 离开频道事件
  callService.on('leaveChannel', () => {
    stopCallTimer();
  });

  // 用户离开事件
  callService.on('userLeave', () => {
    handleHangup();
  });
}
// 发起通话
const initiateCall = async () => {
  if (remoteUserId.value) {
    try {
      // @ts-ignore
      const nim = uni.$UIKitNIM?.getNIM();
      if (!nim) {
        console.error('NIM未初始化');
        return;
      }

      // 生成通话频道ID
      channelId.value = `call_${Date.now()}`;

      // 使用V2NIMSignallingService发起通话
      const params = {
        calleeAccountId: remoteUserId.value,
        requestId: `req_${Date.now()}`,
        channelType: 1, // 1: 点对点通话
        channelName: channelId.value,
        channelExtension: '',
        serverExtension: '',
        signallingConfig: {
          unreadEnabled: true,
          offlineEnabled: true,
          selfUid: imOptions.accid
        },
        pushConfig: {
          pushEnabled: true,
          pushTitle: callType.value === 1 ? '语音通话' : '视频通话',
          pushContent: `${remoteUserName.value || '用户'}邀请您进行${callType.value === 1 ? '语音' : '视频'}通话`,
          pushPayload: JSON.stringify({ type: callType.value })
        },
        rtcConfig: {
          rtcChannelName: channelId.value,
          rtcTokenTtl: 3600,
          rtcParams: ''
        }
      };

      // @ts-ignore
      const result = await nim.V2NIMSignallingService.call(params);
      console.log('发起通话成功:', result);

      // 保存请求ID和频道ID
      requestId.value = params.requestId;
      callId.value = channelId.value;
    } catch (error) {
      console.error('发起通话失败:', error);
      uni.showToast({
        title: t('callFailedText'),
        icon: 'none'
      });
    }
  }
}

// 初始化信令服务
const initSignalingService = () => {
  // 获取NIM实例
  // @ts-ignore
  const nim = uni.$UIKitNIM?.getNIM();
  if (!nim) {
    console.error('NIM未初始化，无法初始化信令服务');
    uni.showToast({
      title: 'IM服务未初始化',
      icon: 'none'
    });
    return;
  }

  console.log('NIM实例获取成功');

  // 检查是否有V2NIMSignallingService
  if (!nim.V2NIMSignallingService) {
    console.error('V2NIMSignallingService不可用');
    uni.showToast({
      title: '信令服务不可用',
      icon: 'none'
    });
    return;
  }
};

// 页面加载时获取参数
onLoad((options: any = {}) => {
  console.log('1v1Call页面加载，参数:', options);

  if (options?.type) {
    callType.value = parseInt(options.type);
  }
  if (options?.name) {
    remoteUserName.value = options.name;
  }
  if (options?.avatar) {
    remoteUserAvatar.value = options.avatar;
  }
  if (options?.status) {
    callStatus.value = options.status;
  }
  if (options?.userId) {
    remoteUserId.value = options.userId;
  }
  if (options?.callId) {
    callId.value = options.callId;
    // 如果有callId，说明是被叫方
    isCallInitiator.value = false;
  }
  if (options?.channelId) {
    channelId.value = options.channelId;
  }
  if (options?.requestId) {
    requestId.value = options.requestId;
  }

  // 初始化信令服务
  initSignalingService();

  // 初始化通话引擎
  initCallEngine();

  // 如果是发起通话
  if (callStatus.value === 'calling' && remoteUserId.value && isCallInitiator.value) {
    // 设置为主叫方
    initiateCall();
  }

  // 如果已经接通，开始计时
  if (callStatus.value === 'connected') {
    startCallTimer();
  }
})

// 页面卸载时清理资源
onUnload(() => {
  console.log('页面卸载，清理资源');

  // 停止计时器
  stopCallTimer();

  // 清理引擎资源
  if (isSetup.value) {
    try {
      // 销毁引擎（包含移除事件监听、停止预览、离开频道等操作）
      callService.destroyEngine();
      console.log('引擎资源清理成功');
    } catch (error) {
      console.error('清理引擎资源失败:', error);
    } finally {
      isSetup.value = false;
    }
  }

  // 如果是通话未结束就退出页面，则关闭频道
  if (callStatus.value !== 'ended' && channelId.value) {
    try {
      // @ts-ignore
      const nim = uni.$UIKitNIM?.getNIM();
      if (nim && nim.V2NIMSignallingService) {
        // 使用V2NIMSignallingService关闭频道
        nim.V2NIMSignallingService.close({
          channelId: channelId.value,
          offlineEnabled: true
        }).then(() => {
          console.log('退出页面，关闭频道成功');
        }).catch((error: any) => {
          console.error('退出页面，关闭频道失败:', error);
        });
      }
    } catch (error) {
      console.error('退出页面，关闭频道失败:', error);
    }
  }
})

// 处理本地视图加载完成
const handleLocalViewLoad = () => {
  console.log('本地视图加载完成');
  if (isSetup.value && callType.value === 2) {
    try {
      // 设置本地视频画布，包含view参数
      callService.setupLocalVideoCanvas({
        view: imOptions.accid, // 添加view参数，对应NertcLocalView的viewID
        isMediaOverlay: false
      });

      // 开始预览
      callService.startPreview();

      // 如果还没有加入频道，现在加入
      if (callStatus.value !== 'connected') {
        joinChannel();
      }
    } catch (error) {
      console.error('设置本地视频失败:', error);
      uni.showToast({
        title: '视频初始化失败',
        icon: 'none'
      });
    }
  }
}

// 处理远程视图加载完成
const handleRemoteViewLoad = () => {
  console.log('远程视图加载完成');
  if (isSetup.value && callType.value === 2 && remoteUserId.value) {
    try {
      // 设置远程视频画布
      callService.setupRemoteVideoCanvas(remoteUserId.value, {
        view: remoteUserId.value, // 添加view参数，对应NertcRemoteView的viewID
        isMediaOverlay: false
      });

      // 添加延迟，确保视图已经准备好
      setTimeout(() => {
        try {
          // 订阅远程视频
          callService.subscribeRemoteVideo(remoteUserId.value);
        } catch (error) {
          console.error('订阅远程视频失败:', error);
          uni.showToast({
            title: '远程视频订阅失败',
            icon: 'none'
          });
        }
      }, 100);
    } catch (error) {
      console.error('设置远程视频失败:', error);
      uni.showToast({
        title: '远程视频初始化失败',
        icon: 'none'
      });
    }
  }
}

// 加入通话频道
const joinChannel = async () => {
  if (!isSetup.value) {
    console.error('引擎未初始化，无法加入频道');
    return;
  }

  try {
    // 使用channelId作为频道名，如果没有则使用callId或生成一个新的
    const channelName = channelId.value || callId.value || `call_${Date.now()}`;

    // 获取音视频通话Token，传递频道名称和有效期
    const tokenRes = await getImToken({
      channelName: channelName,
      ttlSec: '3600' // 设置token有效期为1小时
    });
    if (tokenRes.code !== 200 || !tokenRes.data) {
      uni.showToast({
        title: '获取通话Token失败',
        icon: 'none'
      });
      return;
    }

    // 获取权限密钥，传递频道名称、权限密钥和有效期
    const keyRes = await getPermissionKey({
      channelName: channelName,
      permSecret: Config.PERM_SECRET, // 使用配置文件中的权限密钥
      ttlSec: '3600', // 设置权限密钥有效期为1小时
      privilege: '0' // 默认权限值
    });
    if (keyRes.code !== 200 || !keyRes.data) {
      uni.showToast({
        title: '获取权限密钥失败',
        icon: 'none'
      });
      return;
    }

    // 保存token和密钥
    const rtcToken = tokenRes.data;
    const permissionKey = keyRes.data;

    console.log('加入通话频道', imOptions);

    console.log('[NERTC-APP] joinChannel() 房间信息:', {
      token: rtcToken,
      channelName: channelName,
      myUid: imOptions.accid
    });

    callService.joinChannel({
      token: rtcToken, // 使用新获取的token
      channelName: channelName,
      myUid: imOptions.accid
    });
  } catch (error) {
    console.error('加入频道失败:', error);
    uni.showToast({
      title: '加入通话失败',
      icon: 'none'
    });
  }
}

</script>

<style lang="scss" scoped>
.call-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #1a1a1a;
  color: #ffffff;
}

.call-status-bar {
  width: 100%;
  padding: 40px 0 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10; /* 确保状态栏在视频上方 */
  background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
  padding-bottom: 30px;

  .call-status {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .call-duration {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }
}

.call-user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40px;

  .call-avatar {
    margin-bottom: 16px;
  }

  .call-username {
    font-size: 24px;
    font-weight: 500;
    margin-top: 8px;
  }
}

.call-video-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100vh;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  .remote-video-view {
    width: 100%;
    height: 100%;
    background-color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }

  .local-video-view {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 120px;
    height: 160px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background-color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    z-index: 2;
  }
}

.call-controls {
  position: absolute;
  bottom: 40px;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10; /* 确保控制按钮在视频上方 */
  background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
  padding-top: 30px;

  .call-action-row {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
    width: 100%;
  }

  .call-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 20px;

    .btn-text {
      font-size: 12px;
      margin-top: 8px;
      color: rgba(255, 255, 255, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
    }

    &.active-btn {
      color: #0089ff;
      background-color: rgba(0, 137, 255, 0.1);
      border-radius: 8px;
      padding: 4px;

      .btn-text {
        color: #0089ff;
      }
    }

    &.decline-btn,
    &.hangup-btn {
      background-color: #ff3b30;
      width: 60px;
      height: 60px;
      border-radius: 30px;
      justify-content: center;
      align-items: center;
      display: flex;

      .btn-text {
        color: #fff;
      }
    }

    &.accept-btn {
      background-color: #34c759;
      width: 60px;
      height: 60px;
      border-radius: 30px;
      justify-content: center;
      align-items: center;
      display: flex;

      .btn-text {
        color: #fff;
      }
    }
  }
}
</style>
