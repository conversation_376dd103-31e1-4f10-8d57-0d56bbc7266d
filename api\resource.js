import request from '../utils/httpRequest';
import Config from "../utils/config.js"

/**
 * 获取音视频通话Token(用户层)
 * @param {Object} params - 请求参数
 * @param {string} [params.channelName] - 频道名称（可选）
 * @param {string} [params.ttlSec] - token有效期（可选）
 * @returns {Promise} - 返回请求结果
 */
export function getImToken(params = {}) {
	return request.get({
		url: Config.APIURL + "/xchat/imToken/usrapi/getToken",
		data: params, // 添加查询参数
		noLoad: true, //不需要加载动画
	}).then(res => res);
}

/**
 * 获取权限密钥(用户层)
 * @param {Object} params - 请求参数
 * @param {string} [params.channelName] - 频道名称（可选）
 * @param {string} [params.permSecret] - 权限秘钥（可选）
 * @param {string} [params.privilege] - 权限值（可选）
 * @param {string} [params.ttlSec] - 有效期(秒)（可选）
 * @returns {Promise} - 返回请求结果
 */
export function getPermissionKey(params = {}) {
	return request.get({
		url: Config.APIURL + "/xchat/imToken/usrapi/getPermissionKey",
		data: params, // 添加查询参数
		noLoad: true, //不需要加载动画
	}).then(res => res);
}
