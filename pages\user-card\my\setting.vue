<template>
  <div class="wrapper">
    <NavBar :title="t('setText')" />
    <div @click="logout" class="logout">
      {{ t('logoutText') }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import NavBar from '../../../components/NavBar.vue'
import { t } from '../../../utils/i18n'
import { customNavigateTo } from '../../../utils/customNavigate'


const logout = () => {
  uni.showModal({
    title: t('logoutText'),
    content: t('logoutConfirmText'),
    showCancel: true,
    success: function (res) {
      if (res.confirm) {
        console.log('用户点击确定')
        const app = getApp()
        app.logout()
      } else if (res.cancel) {
        console.log('用户点击取消')
      }
    },
  })
}
</script>

<style lang="scss" scoped>
@import '../../styles/common.scss';

page {
  padding-top: var(--status-bar-height);
  height: 100vh;
  background-color: rgb(245, 246, 247);
}

.wrapper {
  background-color: rgb(245, 246, 247);
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
}

.setting-list {
  background-color: #fff;
  border-radius: 8px;
  width: 90%;
  margin: 10px auto;
  overflow: hidden;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  height: 45px;
  line-height: 45px;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  .arrow {
    color: #999;
  }
}

.logout {
  background-color: #fff;
  border-radius: 8px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  width: 90%;
  margin: 10px auto;
  color: rgb(244, 22, 22);
}
</style>
