<template>
  <div class="about-page">
    <NavBar :title="t('aboutUsText')" />
    <div class="about-content">
      <!-- 应用信息区域 -->
      <div class="app-info-section">
        <div class="app-logo">
          <image src="/static/logo.png" mode="aspectFit" class="logo-img" />
        </div>
        <h1 class="app-name">X-Chat</h1>
        <p class="app-version">{{ t('currentVersionText') }}: {{ appVersion }}</p>
      </div>

      <!-- 功能列表 -->
      <div class="function-list">
        <div class="function-item" @click="gotoFeatureIntro">
          <div class="item-left">
            <Icon iconClassName="feature-icon" type="icon-kefu" />
            <span class="item-title">{{ t('featureIntroText') }}</span>
          </div>
          <Icon iconClassName="arrow-icon" type="icon-jiantou" />
        </div>

        <div class="function-item" @click="checkVersion">
          <div class="item-left">
            <Icon iconClassName="version-icon" type="icon-computed" />
            <span class="item-title">{{ t('versionCheckText') }}</span>
          </div>
          <div class="item-right">
            <span class="checking-text" v-if="isChecking">{{ t('checkingVersionText') }}</span>
            <Icon iconClassName="arrow-icon" type="icon-jiantou" v-else />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from '../../utils/transformVue'
import NavBar from '../../components/NavBar.vue'
import Icon from '../../components/Icon.vue'
import { t } from '../../utils/i18n'
import { getCurrentVersion, checkForUpdate, performUpdate } from '../../utils/versionCheck'
import { customNavigateTo } from '../../utils/customNavigate'

// 获取应用版本信息
const appVersion = ref(getCurrentVersion())
const isChecking = ref(false)

// 跳转到功能介绍页面
const gotoFeatureIntro = () => {
  customNavigateTo({
    url: '/pages/About/feature-intro'
  })
}

// 检查版本更新
const checkVersion = async () => {
  // #ifdef APP-PLUS
  if (isChecking.value) return

  isChecking.value = true

  try {
    const updateInfo = await checkForUpdate()

    if (updateInfo) {
      // 发现新版本
      uni.showModal({
        title: t('updateAvailableText'),
        content: `发现新版本 ${updateInfo.version}，是否立即更新？`,
        success: (res) => {
          if (res.confirm) {
            performUpdate(updateInfo.updateUrl)
          }
        }
      })
    } else {
      // 已是最新版本
      uni.showToast({
        title: t('latestVersionText'),
        icon: 'success'
      })
    }
  } catch (error) {
    uni.showToast({
      title: t('checkVersionFailedText'),
      icon: 'none'
    })
  } finally {
    isChecking.value = false
  }
  // #endif

  // #ifndef APP-PLUS
  uni.showToast({
    title: '仅支持APP版本检查更新',
    icon: 'none'
  })
  // #endif
}
</script>

<style lang="scss" scoped>
.about-page {
  min-height: 100vh;
  background-color: #f5f6f7;
}

.about-content {
  padding: 20px;
}

.app-info-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 30px 20px;
  text-align: center;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .app-logo {
    margin-bottom: 20px;

    .logo-img {
      width: 80px;
      height: 80px;
      border-radius: 16px;
    }
  }

  .app-name {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
  }

  .app-version {
    font-size: 14px;
    color: #666;
  }
}

.function-list {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.function-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f9fa;
  }

  .item-left {
    display: flex;
    align-items: center;

    .feature-icon,
    .version-icon {
      margin-right: 12px;
      color: #337EFF;
      font-size: 20px;
    }

    .item-title {
      font-size: 16px;
      color: #333;
    }
  }

  .item-right {
    display: flex;
    align-items: center;

    .checking-text {
      font-size: 14px;
      color: #666;
    }

    .arrow-icon {
      color: #999;
      font-size: 16px;
    }
  }
}
</style>
