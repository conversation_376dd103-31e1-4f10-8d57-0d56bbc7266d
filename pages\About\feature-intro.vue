<template>
  <div class="version-history-page">
    <NavBar :title="t('featureIntroText')" />
    <div class="version-content">
      <div class="version-list">
        <div class="version-item" v-for="(version, index) in versionHistory" :key="index">
          <div class="version-header">
            <div class="version-info">
              <span class="version-number">v{{ version.version }}</span>
              <span class="version-tag" v-if="version.isLatest">最新</span>
            </div>
            <span class="release-date">{{ version.date }}</span>
          </div>

          <div class="version-content-section">
            <h3 class="update-title">更新内容</h3>
            <ul class="update-list">
              <li v-for="(item, idx) in version.features" :key="idx" class="update-item">
                <span class="update-type" :class="item.type">{{ getUpdateTypeText(item.type) }}</span>
                <span class="update-desc">{{ item.description }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from '../../utils/transformVue'
import NavBar from '../../components/NavBar.vue'
import { t } from '../../utils/i18n'

// 版本历史数据
const versionHistory = ref([
  {
    version: '1.0.1',
    date: '2024-12-20',
    isLatest: true,
    features: [
      { type: 'new', description: '新增关于X-Chat页面' },
      { type: 'new', description: '新增版本检查功能' },
      { type: 'new', description: '新增版本历史查看' },
      { type: 'optimize', description: '优化用户界面体验' },
      { type: 'fix', description: '修复已知问题' }
    ]
  },
  {
    version: '1.0.0',
    date: '2024-12-19',
    isLatest: false,
    features: [
      { type: 'new', description: '初始版本发布' },
      { type: 'new', description: '支持即时通讯功能' },
      { type: 'new', description: '添加音视频通话' },
      { type: 'new', description: '完善用户管理' },
      { type: 'new', description: '支持照护计划管理' },
      { type: 'new', description: '身份绑定功能' }
    ]
  }
])

// 获取更新类型文本
const getUpdateTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'new': '新增',
    'optimize': '优化',
    'fix': '修复',
    'remove': '移除'
  }
  return typeMap[type] || '更新'
}
</script>

<style lang="scss" scoped>
.version-history-page {
  min-height: 100vh;
  background-color: #f5f6f7;
}

.version-content {
  padding: 20px;
}

.version-list {
  .version-item {
    background-color: #fff;
    border-radius: 12px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafbfc;

  .version-info {
    display: flex;
    align-items: center;

    .version-number {
      font-size: 18px;
      font-weight: bold;
      color: #337EFF;
      margin-right: 8px;
    }

    .version-tag {
      background-color: #52c41a;
      color: #fff;
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 10px;
    }
  }

  .release-date {
    font-size: 14px;
    color: #666;
  }
}

.version-content-section {
  padding: 20px;

  .update-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 12px;
  }
}

.update-list {
  list-style: none;
  padding: 0;
  margin: 0;

  .update-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .update-type {
      display: inline-block;
      font-size: 12px;
      padding: 2px 6px;
      border-radius: 4px;
      margin-right: 8px;
      flex-shrink: 0;
      font-weight: 500;

      &.new {
        background-color: #e6f7ff;
        color: #1890ff;
      }

      &.optimize {
        background-color: #f6ffed;
        color: #52c41a;
      }

      &.fix {
        background-color: #fff2e8;
        color: #fa8c16;
      }

      &.remove {
        background-color: #fff1f0;
        color: #ff4d4f;
      }
    }

    .update-desc {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
      flex: 1;
    }
  }
}
</style>
