<template>
  <view>
		<ConversationList />
		<EjectDrawer mode="bottom" :visible="visible" drawerWidth="100%" :isRoundBox='true' drawerHeight="744rpx">
			<Dept :deptList="deptList" @close='closeVisible' />
		</EjectDrawer>
	</view>
</template>

<script lang="ts" setup>
import ConversationList from './conversation-list/index.vue'
import { trackInit } from '../../utils/reporter'
import { onShow } from '@dcloudio/uni-app'
import { ref, onMounted } from '../../utils/transformVue'
import EjectDrawer from "@/components/eject-drawer.vue"
import Dept from "./components/dept.vue"
import {
	companyList,
} from "@/api/index.js"
import {
	CLIENT_ID
} from "@/utils/config"

import { setContactTabUnread, setTabUnread } from '../../utils/msg'

trackInit('ConversationUIKit')

const deptList = ref([])
const visible = ref(false)

onMounted(()=>{
	companyListFun();
})

const closeVisible = ()=>{
	visible.value = false;
}

const companyListFun = ()=>{
	const userInfo = uni.getStorageSync('userInfo')
	if(!userInfo?.deptId){
		const params = {
			clientid:CLIENT_ID
		}
		companyList(params).then(res=>{
			deptList.value = res?.data || [];
			if(deptList.value.length > 0){
				visible.value = true
			}
		})
	}
}

onShow(() => {
  // 重置选中会话
  // @ts-ignore
  uni.$UIKitStore?.uiStore.selectSession('')
  setTabUnread()
  setContactTabUnread()
})
</script>

<style lang="scss">
@import '../styles/common.scss';

page {
  height: 100vh;
  overflow: hidden;
}
</style>
