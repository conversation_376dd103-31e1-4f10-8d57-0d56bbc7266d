/**
 * 防抖函数
 * @param {Function} fn 需要防抖的函数
 * @param {Number} delay 延迟时间，单位毫秒
 * @param {Boolean} immediate 是否立即执行
 * @returns {Function} 防抖处理后的函数
 */
function debounce(fn, delay = 300, immediate = false) {
  let timer = null;
  let isInvoked = false;
  
  return function(...args) {
    const context = this;
    
    // 如果是立即执行且未被调用过
    if (immediate && !isInvoked) {
      fn.apply(context, args);
      isInvoked = true;
      return;
    }
    
    // 清除之前的定时器
    if (timer) clearTimeout(timer);
    
    // 设置新的定时器
    timer = setTimeout(() => {
      fn.apply(context, args);
      isInvoked = false;
    }, delay);
  };
}

/**
 * 节流函数
 * @param {Function} fn 需要节流的函数
 * @param {Number} delay 延迟时间，单位毫秒
 * @returns {Function} 节流处理后的函数
 */
function throttle(fn, delay = 300) {
  let lastTime = 0;
  
  return function(...args) {
    const context = this;
    const now = Date.now();
    
    if (now - lastTime >= delay) {
      fn.apply(context, args);
      lastTime = now;
    }
  };
}

/**
 * 防抖函数（带有请求合并功能）
 * @param {Function} fn 需要防抖的函数
 * @param {Number} delay 延迟时间，单位毫秒
 * @param {Function} mergeRequests 合并请求的函数，接收一个数组参数
 * @returns {Function} 防抖处理后的函数
 */
function debounceWithMerge(fn, delay = 300, mergeRequests) {
  let timer = null;
  let requests = [];
  
  return function(request) {
    const context = this;
    
    // 添加到请求队列
    requests.push(request);
    
    // 清除之前的定时器
    if (timer) clearTimeout(timer);
    
    // 设置新的定时器
    timer = setTimeout(() => {
      if (typeof mergeRequests === 'function') {
        // 如果提供了合并函数，使用合并函数处理请求
        const mergedRequest = mergeRequests(requests);
        fn.call(context, mergedRequest);
      } else {
        // 否则，只处理最后一个请求
        fn.call(context, requests[requests.length - 1]);
      }
      // 清空请求队列
      requests = [];
    }, delay);
  };
}

export { debounce, throttle, debounceWithMerge };
