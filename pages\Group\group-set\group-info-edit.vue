<template>
  <div class="group-set-container">
    <NavBar :title="t('teamTitle')" />
    <div class="info-box">
			<div class="group-name-input-container">
			  <FormInput
			    :model-value="teamName"
			    :allow-clear="true"
			    :maxlength="maxlength"
			    @input="handleInput"
			  />
			  <div class="input-length">{{ inputLengthTips }}</div>
			</div>
			<div class="avatar-box">
			  <div>群头像</div>
				<image class="camera" :src="teamAvatar" mode="widthFix" @click="changeAvatar" />
			</div>
		</div>
    <div
      :class="getUniPlatform() === 'mp-weixin' ? 'ok-btn-mp' : 'ok-btn'"
      @tap="handleSave"
    >
      {{ t('saveText') }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import NavBar from '../../../components/NavBar.vue'
import FormInput from '../../../components/FormInput.vue'
// @ts-ignore
import { onLoad } from '@dcloudio/uni-app'
import { ref, computed } from '../../../utils/transformVue'

import { t } from '../../../utils/i18n'
import { getUniPlatform } from '../../../utils'

const maxlength = 15
const teamName = ref<string>()
const teamAvatar = ref<string>()
let teamId = ''
let teamInfo = {}

const inputLengthTips = computed(() => {
  return `${teamName.value ? teamName.value?.length : 0}/${maxlength}`
})

// 保存群名称
const handleSave = () => {
  if (!teamName.value) {
    uni.showToast({
      title: t('teamTitleConfirmText'),
      icon: 'error',
    })
    return
  }
  // @ts-ignore
  teamName.value &&
    uni.$UIKitStore.teamStore
      .updateTeamActive({ teamId, name: teamName.value,avatar:teamAvatar.value })
      .then(() => {
        uni.showToast({
          title: t('updateTeamSuccessText'),
          icon: 'success',
        })
        uni.navigateBack()
      })
      .catch((err: any) => {
        switch (err?.code) {
          case 802:
            uni.showToast({
              title: t('noPermission'),
              icon: 'error',
            })
            break
          default:
            uni.showToast({
              title: t('updateTeamFailedText'),
              icon: 'error',
            })
            break
        }
      })
}

const handleInput = (value: string) => {
  teamName.value = value ? value.replace(/\s*/g, '') : value
}

const changeAvatar = () => {
	// 调用拍照/选择图片
	uni.chooseImage({
		count: 1,
		sizeType: ['original'],
		success: (res) => {
			uni.showLoading({
				title:'上传中'
			})
			uni?.$UIKitNIM?.getNIM().cloudStorage.uploadFile({
				filePath:res.tempFilePaths[0],
				type:'image'
			}).then(res=>{
				const avatar = res.url
				teamAvatar.value = avatar
			}).finally(()=>{
				uni.hideLoading()
			}).catch(err=>{
				console.log(err,"==");
			})
		}
	})
}

onLoad((option) => {
  teamId = option?.id
  // @ts-ignore
  const team = uni.$UIKitStore.teamStore.teams.get(teamId)
	teamInfo = team
  teamName.value = team ? team.name.substring(0, 15) : ''
  teamAvatar.value = team ? team.avatar : ''
})
</script>

<style lang="scss" scoped>
@import '../../styles/common.scss';

page {
  padding-top: var(--status-bar-height);
  height: 100vh;
  overflow: hidden;
}

.info-box{
	background: #ffffff;
	width: 90%;
	border-radius: 10rpx;
	margin: 40rpx auto;
	padding: 20rpx 40rpx;
	box-sizing: border-box;
	.avatar-box{
		display: flex;
		align-items: center;
		justify-content: space-between;
		// margin: 10px 20px;
		margin-top: 20rpx;
		.camera{
			width: 80rpx;
			height: 80rpx;
		}
	}
}

.group-set-container {
  height: 100vh;
  box-sizing: border-box;
  background-color: #eff1f4;
}

.group-name-input-container {
  background: #ffffff;
  border-radius: 8px;
  // padding: 0 16px 5px;
  position: relative;
  // margin: 10px 20px;

  .input-length {
    position: absolute;
    right: 25px;
    bottom: 5px;
    font-size: 12px;
    color: #999999;
  }

  ::v-deep.form-input-item {
    border: none;
  }
}
</style>
