import { sm2 } from 'sm-crypto'

export class CryptoUtil {
  /**
   * 使用SM2国密算法加密手机号
   * @param phoneNumber 11位手机号码
   * @param publicKey 服务器返回的SM2公钥
   * @returns 加密后的base64字符串
   */
  static encryptWithSM2(phoneNumber: string, publicKey: string): string {
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
      throw new Error('无效的手机号格式')
    }

    // 验证公钥不能为空
    if (!publicKey) {
      throw new Error('公钥不能为空')
    }

    try {
      // 使用C1C3C2格式，输出base64编码结果
      const encrypted = sm2.doEncrypt(
        phoneNumber,
        publicKey,
        1,  // 输出模式：0-返回16进制字符串，1-返回base64字符串
        { 
          mode: 'cbc',   // 加密模式（国标推荐）
          hash: true     // 对明文先做SM3哈希，增强安全性
        }
      )

      return encrypted
    } catch (error) {
      console.error('加密失败:', error)
      throw new Error('加密过程出错')
    }
  }
}
