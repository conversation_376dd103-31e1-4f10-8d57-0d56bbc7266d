<template>
  <div>
    <image src="@/static/logo.png" class="welcome-img" mode="widthFix" />
    <button class="login-btn" @click="onClick">注册/登录</button>
    <!-- <div class="bottom-box">
      <image src="https://yx-web-nosdn.netease.im/common/9303d9be2ea5f90c48397326ae5dfd45/welcome-bottom.png" class="welcome-img-bottom" />
    </div> -->
  </div>
</template>

<script lang="ts" setup>

const onClick = () => {
  uni.$emit('login')
}

// const url = 'https://yiyong-qa.netease.im/yiyong-static/statics/uniapp-vue2-h5'

</script>

<style lang="scss" scoped>
page {
  height: 100vh;
  overflow: hidden;
}

.welcome-img {
  width: 120px;
  height: auto;
  display: block;
  margin: 20vh auto 0;
}

.login-btn {
  height: 50px;
  width: 90%;
  background: #337EFF;
  border-radius: 8px;
  color: #fff;
  margin-top: 80px;
}

.bottom-box {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);

  .welcome-img-bottom {
    width: 82px;
    height: 20px;
  }
}
</style>
