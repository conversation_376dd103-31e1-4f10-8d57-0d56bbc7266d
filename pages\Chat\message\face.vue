<template>
  <div class="msg-face-wrapper">
    <div class="msg-face">
      <div class="msg-face-row" v-for="(emojiRow, rowIndex) in emojiMatrix">
        <div
          @tap.stop="
            () => {
              handleEmojiClick({ key, type: emojiMap[key] })
            }
          "
          v-for="key in emojiRow"
          :key="key"
          class="msg-face-item"
        >
          <Icon :size="27" :type="emojiMap[key]"></Icon>
        </div>
        <!-- 下面放三个看不到的 Icon 占个位 -->
        <Icon
          v-if="rowIndex + 1 === Math.ceil(emojiArr.length / emojiColNum)"
          class="msg-face-delete"
          :size="27"
          type="icon-tuigejian"
        ></Icon>
        <Icon
          v-if="rowIndex + 1 === Math.ceil(emojiArr.length / emojiColNum)"
          class="msg-face-delete"
          :size="27"
          type="icon-tuigejian"
        ></Icon>
        <Icon
          v-if="rowIndex + 1 === Math.ceil(emojiArr.length / emojiColNum)"
          class="msg-face-delete"
          :size="27"
          type="icon-tuigejian"
        ></Icon>
      </div>
    </div>
    <div class="emoji-block"></div>
   <div class="msg-face-control">
      <div @tap="handleEmojiDelete" class="msg-delete-btn">
        <Icon type="icon-tuigejian" :size="25" :color="'#333'" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { emojiMap } from '../../../utils/emoji'
import { culculateMatrix } from '../../../utils/matrix'
import Icon from '../../../components/Icon.vue'
import { t } from '../../../utils/i18n'
// 七个一行
const emojiArr = Object.keys(emojiMap)
const emojiColNum = 7
const emojiMatrix = culculateMatrix(emojiArr, emojiColNum)

const emit = defineEmits(['emojiClick', 'emojiSend', 'emojiDelete'])

const handleEmojiClick = (emoji: any) => {
  emit('emojiClick', emoji)
}

const handleEmojiDelete = () => {
  emit('emojiDelete')
}

const handleEmojiSend = () => {
  emit('emojiSend')
}
</script>

<style scoped lang="scss">
.msg-face-wrapper {
  box-sizing: border-box;
}
.msg-face-control {
  position: fixed;
  bottom: 10px;
  right: 10px;
  left: 10px;
  z-index: 8;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 10px;
  margin-bottom: 50px; /* 为底部固定输入框留出空间 */
}

.emoji-block {
  width: 100%;
  height: 40px;
  background-color: transparent;
}

.msg-face {
  display: flex;
  flex-direction: column;
  padding-bottom: 10px;
  // flex-wrap: wrap;

  &-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;

    &:last-child {
      flex-basis: 57.14%;
    }
  }

  &-item {
    font-size: 30px;
    transition: all 0.2s ease;

    &:active {
      transform: scale(1.2);
    }
  }

  &-delete {
    font-size: 27px;
    visibility: hidden;
  }
}

.msg-face-control {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.msg-send-btn {
  width: 90rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #2A6BF2;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-shadow: 0 2px 4px rgba(42, 107, 242, 0.2);
}

.msg-delete-btn {
  background-color: #fff;
  margin-right: 15px;
  padding: 0 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e8e8e8;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
