<template>
  <div class="video-wrapper">
    <!-- 移除导航栏，添加自定义返回按钮（仅在H5平台显示） -->
    <!-- #ifdef H5 -->
    <div class="back-button" @tap="goBack">
      <Icon type="icon-zuojiantou" :size="22" color="#FFFFFF" />
    </div>
    <!-- #endif -->
    <div class="video-box">
      <video v-if="show" class="video" :src="videoUrl" id="videoEle" controls autoplay></video>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "../../utils/transformVue";
import { onLoad, onReady } from "@dcloudio/uni-app";
import Icon from "../../components/Icon.vue";

const videoUrl = ref();
const show = ref(false);
onLoad((option: any) => {
  videoUrl.value = decodeURIComponent(option.videoUrl);
  show.value = true;
});

onReady(() => {
  show.value = true;
});

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1
  });
};
</script>
<style lang="scss" scoped>
.video-wrapper{
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  background: #000000;
  display: flex;
  flex-direction: column;
  position: relative;
}

.back-button {
  position: absolute;
  top: calc(var(--status-bar-height) + 10px);
  left: 15px;
  z-index: 100;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.925);
  border-radius: 50%;
  opacity: 0.7;
  transition: opacity 0.3s;

  &:active {
    opacity: 1;
  }
}

.video-box {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  .video {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>