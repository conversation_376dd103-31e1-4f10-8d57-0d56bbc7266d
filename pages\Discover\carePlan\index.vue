<template>
	<view class="care_plan_page">
		<uni-card v-if="list.length" :title="item.timeSlot" :sub-title="item.caregiverName" :extra="type[item.progressStatus]" v-for="(item,index) in list" :key="index" @click="bindClick(item)">
			<view>老人：{{item.elderName}}</view>
			<view>护理动作：{{item.activity}}</view>
		</uni-card>
		<Empty
		v-else
		  text="暂无护理数据"
		/>
	</view>
</template>

<script>
	import { customNavigateTo } from '../../../utils/customNavigate'
	import { careTaskRecordList } from "@/api/index"
	import Empty from '../../../components/Empty.vue'
	export default {
		data() {
			return {
				list: [],
				type:{
					0:'待开始', 
					1:'进行中',
					2:'已完成', 
					3:'未达标', 
					4:'异常中断', 
					5:'已取消'
				},
				nurseId:uni.getStorageSync("userInfo").nurseId,
			};
		},
		components:{
			Empty
		},
		created() {
			careTaskRecordList().then(res => {
				console.log(res)
				this.list = res.data
			})
		},
		mounted() {
			
		},
		methods: {
			bindClick(item) {
				customNavigateTo({
				  url: `/pages/Discover/carePlan/details?id=${item.id}`,
				})
			}
		}
	}
</script>

<style lang="scss">

</style>