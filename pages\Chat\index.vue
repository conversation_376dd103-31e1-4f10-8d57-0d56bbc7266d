<template>
  <!-- 处理滚动穿透  此为官方推荐做法 https://uniapp.dcloud.net.cn/component/uniui/uni-popup.html#%E4%BB%8B%E7%BB%8D -->
  <page-meta
    :page-style="'overflow:' + (moveThrough ? 'hidden' : 'visible')"
  ></page-meta>
  <div :class="isH5 ? 'msg-page-wrapper-h5' : 'msg-page-wrapper'">
    <NavBar :title="title" :showLeft="true">
      <template v-slot:left>
        <div @click="backToConversation">
          <Icon type="icon-zuojiantou" :size="22"></Icon>
        </div>
      </template>
			<template v-slot:right>
			  <div class="msg-input-button" @tap="handleSetting">
			    <Icon type="icon-More" :size="20" />
			  </div>
			</template>
    </NavBar>
    <div class="msg-alert">
      <NetworkAlert />
    </div>
    <div :class="isH5 ? 'msg-wrapper-h5' : 'msg-wrapper'">
      <MessageList
        :scene="scene"
        :to="to"
        :msgs="msgs"
        :loading-more="loadingMore"
        :no-more="noMore"
        :reply-msgs-map="replyMsgsMap"
      />
    </div>
    <div style="height: 'auto'">
      <MessageInput :reply-msgs-map="replyMsgsMap" :scene="scene" :to="to" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onHide, onShow } from '@dcloudio/uni-app'
import { events } from '../../utils/constants'
import { trackInit } from '../../utils/reporter'
import { autorun } from 'mobx'
import { ref, onMounted, onUnmounted } from '../../utils/transformVue'
import { parseSessionId } from '../../utils/msg'
import type { TMsgScene } from '@xkit-yx/im-store'
import { deepClone, getUniPlatform } from '../../utils'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { customSwitchTab } from '../../utils/customNavigate'
import NetworkAlert from '../../components/NetworkAlert.vue'
import NavBar from './message/nav-bar.vue'
import Icon from '../../components/Icon.vue'
import MessageList from './message/message-list.vue'
import MessageInput from './message/message-input.vue'
import type { IMMessage } from 'nim-web-sdk-ng/dist/NIM_MINIAPP_SDK/MsgServiceInterface'
import { HISTORY_LIMIT, MSG_ID_FLAG } from '../../utils/constants'
import { t } from '../../utils/i18n'
import { clear } from 'console'
import { customNavigateTo } from '../../utils/customNavigate'
import { chatLog,updateImStatus } from "../../api/index"
import { debounce } from "../../utils/debounce"

trackInit('ChatUIKit')

const title = ref('')
// @ts-ignore
// 获取当前选中的会话ID（从全局状态管理库中获取）
const sessionId = uni.$UIKitStore.uiStore.selectedSession
// 解析会话ID获取消息场景和接收方信息
const { scene, to }: { scene: TMsgScene; to: string } = parseSessionId(sessionId)
// 判断当前运行平台是否为H5环境
const isH5 = getUniPlatform() === 'web'

// 处理uni-popup 引起的滚动穿透
const moveThrough = ref(false)

const backToConversation = () => {
  customSwitchTab({
    url: '/pages/Conversation/index',
  })
}

// 跳转设置页
const handleSetting = () => {
  if (scene === 'p2p') {
    customNavigateTo({
      url: `/pages/Chat/message/p2p-set?id=${to}`,
    })
  } else if (scene === 'team') {
    customNavigateTo({
      url: `/pages/Group/group-set/index?id=${to}`,
    })
  }
}

let isMounted = false

const loadingMore = ref(false)
const noMore = ref(false)

// @ts-ignore
const myAccount = uni.$UIKitStore.userStore.myUserInfo.account
const userInfo = uni.$UIKitStore.userStore.myUserInfo;

const msgs = ref<IMMessage[]>([])

// 发送消息的用户
let toUserInfo:any = null
// 发送消息的群
let toTeamInfo:any = null

// 回复
const replyMsgsMap = ref<Record<string, IMMessage>>()

const handleDismissTeam = (data: any) => {
  if (data.teamId === to) {
    uni.showModal({
      content: t('onDismissTeamText'),
      showCancel: false,
      success(data) {
        if (data.confirm) {
          backToConversation()
        }
      },
    })
  }
}

const handleRemoveTeamMembers = (data: any) => {
  if (data.team.teamId === to && data.accounts.includes(myAccount)) {
    uni.showModal({
      content: t('onRemoveTeamText'),
      showCancel: false,
      success(data) {
        if (data.confirm) {
          backToConversation()
        }
      },
    })
  }
}

const handleMsg = (msg: IMMessage) => {
  uni.$emit(events.ON_SCROLL_BOTTOM, msg)
}

const handleSyncOfflineMsgs = () => {
  const timer = setTimeout(() => {
    uni.$emit(events.ON_SCROLL_BOTTOM)
    clearTimeout(timer)
  }, 300)
}

const getHistory = async (endTime: number, lastMsgId?: string) => {
  try {
    if (noMore.value) {
      return []
    }
    if (loadingMore.value) {
      return []
    }
    loadingMore.value = true
    if (sessionId) {
      // @ts-ignore
      const historyMsgs = await uni.$UIKitStore.msgStore.getHistoryMsgActive({
        sessionId,
        endTime,
        lastMsgId,
        limit: HISTORY_LIMIT,
      })
      loadingMore.value = false
      if (historyMsgs.length < HISTORY_LIMIT) {
        noMore.value = true
      }
      return historyMsgs
    }
  } catch (error) {
    loadingMore.value = false
    throw error
  }
}

const handleLoadMore = async (lastMsg: IMMessage) => {
  const res = await getHistory(lastMsg.time, lastMsg.idServer)
  return res
}

onShow(function () {
  autorun(() => {
    if (scene === 'p2p') {
      // @ts-ignore
      title.value = deepClone(
        // @ts-ignore
        uni.$UIKitStore.uiStore.getAppellation({ account: to })
      )
    } else if (scene === 'team') {
      // @ts-ignore
      const team = uni.$UIKitStore.teamStore.teams.get(to)
      const subTitle = `(${team?.memberNum || 0})`
      title.value = (team?.name || '') + subTitle
    }
  })
})

onLoad(() => {
  uni.$on(events.HANDLE_MOVE_THROUGH, (flag) => {
    moveThrough.value = flag
  })
  // @ts-ignore
  uni.$currentAudioContext = ''
})

onMounted(() => {
  const timer = setTimeout(() => {
    uni.$emit(events.ON_SCROLL_BOTTOM)
    clearTimeout(timer)
  }, 300)

  if (scene === 'p2p') {
    // @ts-ignore
    title.value = deepClone(
      // @ts-ignore
      uni.$UIKitStore.uiStore.getAppellation({ account: to })
    )
  } else if (scene === 'team') {
    // @ts-ignore
    const team = uni.$UIKitStore.teamStore.teams.get(to)
    const subTitle = `(${team?.memberNum || 0})`
    title.value = (team?.name || '') + subTitle
  }

  // @ts-ignore
  // 监听新消息事件（实时消息、漫游消息、离线消息）
  // 参数：msg - 消息对象，包含消息内容、发送者等信息
  // 处理：更新消息列表、未读数等
  uni.$UIKitNIM.on('msg', handleMsg)

  // @ts-ignore
  // 监听群解散事件（当群主解散群时触发）
  // 参数：team - 被解散的群组对象
  // 处理：关闭聊天窗口、清除本地群组数据
  uni.$UIKitNIM.on('dismissTeam', handleDismissTeam)

  // @ts-ignore
  // 监听群成员移除事件（管理员移除成员时触发）
  // 参数：{teamId, accounts} - 群ID和被移除成员账号列表
  // 处理：更新群成员列表，如果自己被移除则退出聊天
  uni.$UIKitNIM.on('removeTeamMembers', handleRemoveTeamMembers)

  // @ts-ignore
  // 监听离线消息同步完成事件（网络恢复后触发）
  // 参数：msgs - 离线期间收到的消息数组
  // 处理：批量插入消息列表，保持消息顺序
  uni.$UIKitNIM.on('syncOfflineMsgs', handleSyncOfflineMsgs)
// 监听获取历史消息事件
  uni.$on(events.GET_HISTORY_MSG, (msg: IMMessage) => {
    handleLoadMore(msg) // 加载更多消息处理
      .then((res: IMMessage[]) => {
        if (res?.[0]) {
          // uni.pageScrollTo 微信小程序指定滚动位置不起作用 https://ask.dcloud.net.cn/question/173874?item_id=258278&rf=false
          setTimeout(() => {
            uni.pageScrollTo({
              selector: `#${MSG_ID_FLAG + res[0].idClient}`,
              scrollTop: 0,
              duration: 0,
              fail: (error) => {
                console.log('error', error)
              },
            })
          }, 300) // 滚动到第一条新加载的消息位置
        }
      })
      .finally(() => {
        // uni.stopPullDownRefresh();
      })
  })
})

onUnload(() => {
  uni.$off(events.CONFIRM_FORWARD_MSG) // 移除全局消息转发确认事件监听
  uni.$off(events.CANCEL_FORWARD_MSG) // 移除全局消息转发取消事件监听
})

const uninstallHistoryWatch = autorun(() => {  // 创建一个响应式监听器，当依赖的observable变化时自动执行
  // @ts-ignore
  if (uni.$UIKitStore.connectStore.connectState === 'connected') {  // 检查连接状态是否为已连接
    getHistory(Date.now()).then(() => {
      if (!isMounted) {
        uni.$emit(events.ON_SCROLL_BOTTOM) // 触发滚动到底部事件
        isMounted = true // 标记组件已挂载，防止重复触发
      }
    })
  }
})

function getYMDHMS (timestamp) {
　　let time = new Date(timestamp)
　　let year = time.getFullYear()
　　let month = time.getMonth() + 1
　　let date = time.getDate()
　　let hours = time.getHours()
　　let minute = time.getMinutes()
　　let second = time.getSeconds()

　　if (month < 10) { month = '0' + month }
　　if (date < 10) { date = '0' + date }
　　if (hours < 10) { hours = '0' + hours }
　　if (minute < 10) { minute = '0' + minute }
　　if (second < 10) { second = '0' + second }
　　return year + '-' + month + '-' + date + ' ' + hours + ':' + minute + ':' + second
}

// 用于记录已处理过的消息ID，避免重复处理
const processedMsgIds = new Set();

// 创建一个防抖版本的chatLog函数，延迟500毫秒执行，避免短时间内重复调用
const debouncedChatLog = debounce((data) => {
  // 检查消息是否已经有URL（确保图片/视频已完全上传）
  if ((data.type === 'image' || data.type === 'video' || data.type === 'audio') && !data.content) {
    console.log('媒体文件尚未完全上传，暂不记录日志');
    return;
  }

  chatLog(data).then(res => {
    // 可以在这里添加成功处理逻辑
    if (res && res.code !== 200) {
      console.log('消息日志记录失败:', res.msg);
    } else {
      console.log('消息日志记录成功:', data.idClient);
    }
  }).catch(err => {
    console.error('消息日志记录错误:', err);
  });
}, 500);

// 动态更新消息
const uninstallMsgsWatch = autorun(async () => {
  // @ts-ignore
  msgs.value = deepClone(uni.$UIKitStore.msgStore.getMsg(sessionId))

  // 遍历所有消息，找出被回复消息，储存在map中
  if (msgs.value.length !== 0) {

		// 私聊消息发送后处理逻辑
		const lastMsg = msgs.value[msgs.value.length -1];

		// 校验消息状态为已发送且发送者为当前用户，并且该消息未被处理过
		if(lastMsg.status == "sent" && lastMsg.idClient && lastMsg.from == userInfo.account && !processedMsgIds.has(lastMsg.idClient)){
			// 将消息ID添加到已处理集合中
			processedMsgIds.add(lastMsg.idClient);

			console.warn('最新消息详情:',lastMsg)

			// 处理消息撤回操作
			if(lastMsg.idClient.indexOf('reCall')!=-1){
				const reCallId = lastMsg.idClient.replace('reCall-','')
				console.log("撤销消息ID：",reCallId)
				const params = {
					idClient:reCallId,
					status:'recall' // 更新消息状态为撤回
				}
				updateImStatus(params) // 调用API更新消息状态
				return
			}

			// 构建消息存储数据结构
			let dbServerData = {
				idClient:lastMsg.idClient,        // 客户端生成的消息ID
				idServer:lastMsg.idServer,        // 服务器生成的消息ID
				scene:lastMsg.scene,               // 消息场景（p2p/team）
				toAccept:lastMsg.to,               // 接收方账号或群ID
				toName:toUserInfo?.nick || '未知',  // 接收方昵称
				toAvatar:toUserInfo?.avatar || '', // 接收方头像
				type:lastMsg.type,                 // 消息类型（text/image等）
				content:lastMsg.body || '其他类型消息，暂不支持解析', // 消息内容
				fromAccount:userInfo.account,      // 发送方账号
				fromNick:userInfo.nick || userInfo.account        ,            // 发送方昵称
				fromAvatar:userInfo.avatar,        // 发送方头像
				fromDeviceId:lastMsg.fromDeviceId, // 发送设备ID
				time:getYMDHMS(lastMsg.time),      // 格式化后的发送时间
				userUpdateTime:getYMDHMS(lastMsg.userUpdateTime), // 更新时间
				status:"sent",                     // 消息状态
				ext:lastMsg.ext || undefined       // 扩展字段
			}
			if(lastMsg.scene == 'p2p'){
				if(!toUserInfo){
					toUserInfo = await uni.$UIKitStore.userStore.getUserForceActive(lastMsg.to)
				}
				dbServerData.toName = toUserInfo.nick || toUserInfo.account
				dbServerData.toAvatar = toUserInfo.avatar
			}else if(lastMsg.scene == 'team' || lastMsg.scene == 'superTeam'){
				if(!toTeamInfo){
					toTeamInfo = uni.$UIKitStore.teamStore.teams.get(lastMsg.to)
				}
				dbServerData.toName = toTeamInfo.name
				dbServerData.toAvatar = toTeamInfo.avatar
			}
			if(lastMsg.type == 'image' || lastMsg.type == 'video' || lastMsg.type == 'audio'){
				// 确保媒体文件已完全上传（有URL）
				dbServerData.content = lastMsg.attach?.url || ''

				// 如果没有URL，说明媒体文件尚未完全上传，不记录日志
				if (!dbServerData.content) {
					console.log('媒体文件尚未完全上传，跳过日志记录:', lastMsg.idClient);
					return;
				}
			}else if(lastMsg.type == 'text'){
				dbServerData.content = lastMsg.body
			}
			console.warn('dbServerData',dbServerData);
			if(['image','video','audio','text'].includes(dbServerData.type)){
				// 使用防抖版本的chatLog函数
				debouncedChatLog(dbServerData)
			}
		}
    const _replyMsgsMap: any = {}
    const reqMsgs: Array<{
      scene: 'p2p' | 'team'
      from: string
      to: string
      idServer: string
      time: number
    }> = []
    const idClients: Record<string, string> = {}
    msgs.value.forEach((msg) => {
      if (msg.ext) {
        try {
          // yxReplyMsg 存储着被回复消息的相关消息
          const { yxReplyMsg } = JSON.parse(msg.ext)
          if (yxReplyMsg) {
            // 从消息列表中找到被回复消息，replyMsg 为被回复的消息
            const replyMsg = msgs.value.find(
              (item) => item.idClient === yxReplyMsg.idClient
            )
            // 如果直接找到，存储在map中
            if (replyMsg) {
              _replyMsgsMap[msg.idClient] = replyMsg
              // 如果没找到，说明被回复的消息可能有三种情况：1.被删除 2.被撤回 3.不在当前消息列表中（一次性没拉到，在之前的消息中）
            } else {
              _replyMsgsMap[msg.idClient] = { idClient: 'noFind' }
              const { scene, from, to, idServer, time } = yxReplyMsg
              if (scene && from && to && idServer && time) {
                reqMsgs.push({ scene, from, to, idServer, time })
                idClients[idServer] = msg.idClient
              }
            }
          }
        } catch {}
      }
    })

    if (reqMsgs.length > 0) {
      // 从服务器拉取被回复消息, 但是有频率控制
      // @ts-ignore
      uni.$UIKitStore.msgStore
        .getMsgByIdServerActive({ reqMsgs })
        .then((res: IMMessage[]) => {
          if (res?.length > 0) {
            res.forEach((item: IMMessage) => {
              if (item.idServer) {
                _replyMsgsMap[idClients[item.idServer]] = item
              }
            })
          }
          replyMsgsMap.value = { ..._replyMsgsMap }
        })
        .catch(() => {
          replyMsgsMap.value = { ..._replyMsgsMap }
        })
    } else {
      replyMsgsMap.value = { ..._replyMsgsMap }
    }
  }

  // 当聊天消息小于6条时，由于页面被键盘撑起，导致已经发出的消息不可见，所以需要隐藏键盘
  if (msgs.value.length < 6) {
    uni.hideKeyboard()
  }
})

onUnmounted(() => {
  // @ts-ignore
  uni.$UIKitNIM.off('dismissTeam', handleDismissTeam)
  // @ts-ignore
  uni.$UIKitNIM.off('removeTeamMembers', handleRemoveTeamMembers)
  // @ts-ignore
  uni.$UIKitNIM.off('msg', handleMsg)
  // @ts-ignore
  uni.$UIKitNIM.off('syncOfflineMsgs', handleSyncOfflineMsgs)

  uni.$off(events.GET_HISTORY_MSG)

  // 清空已处理消息ID集合
  processedMsgIds.clear()

  uninstallHistoryWatch()
  uninstallMsgsWatch()
})
</script>

<style scoped lang="scss">
page {
  height: 100%;
  overflow: hidden;
}

.msg-page-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.msg-page-wrapper-h5 {
  width: 100%;
  height: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
}

.msg-alert {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;
  z-index: 1;
}

.msg-wrapper {
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  flex: 1;
}

.msg-wrapper-h5 {
  width: 100%;
  height: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
}

.msg-wrapper > message-list {
  height: 100%;
}
</style>
