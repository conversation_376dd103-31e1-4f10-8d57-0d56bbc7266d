import { APP_KEY } from "../utils/config.js";
// #ifdef APP-PLUS
import permision from "../NERtcUniappSDK-JS/permission.js";
import NERTC from "../NERtcUniappSDK-JS/lib/index";
import {
  NERTCRenderMode,
  NERTCMirrorMode,
  NERtcVideoStreamType,
  NERtcRemoteVideoStreamType
} from '../NERtcUniappSDK-JS/lib/NERtcDefines';
// #endif

// 定义类型
interface VideoCanvasOptions {
  renderMode?: number;
  mirrorMode?: number;
  view?: string;
  isMediaOverlay?: boolean;
}

interface JoinChannelOptions {
  token: string;
  channelName: string;
  myUid: string | number;
  myStringUid?: string;
  permissionKey: string
}

interface EventData {
  [key: string]: any;
}

class CallService {
  private engine: any = null;
  private isSetup: boolean = false;
  private remoteUserIdVideoList: string[] = [];
  private eventListeners: Map<string, Function[]> = new Map<string, Function[]>();

  // 获取引擎实例
  getEngine() {
    return this.engine;
  }

  // 初始化引擎
  setupEngine() {
    if (this.isSetup) {
      this.destroyEngine()
      this.isSetup = false
      return
    }

    // #ifdef APP-PLUS
    this.engine = NERTC.setupEngineWithContext({
      appKey: APP_KEY,
      logDir: '',
      logLevel: 3
    });
    // #endif
    this.addEventListener()
    this.isSetup = true;
    return this.engine;
  }

  // 添加事件监听
  addEventListener() {
    if (!this.engine) return;

    // #ifdef APP-PLUS
    // 报错事件
    this.engine.addEventListener("onError", (code: number, msg: string, extraInfo: string) => {
      console.log(`[NERTC] onError: code=${code}, msg=${msg}, extraInfo=${extraInfo}`);
      this.triggerEvent('error', { code, msg, extraInfo });
    });

    // 警告事件
    this.engine.addEventListener("onWarning", (code: number, msg: string, extraInfo: string) => {
      console.log(`[NERTC] onWarning: code=${code}, msg=${msg}, extraInfo=${extraInfo}`);
      this.triggerEvent('warning', { code, msg, extraInfo });
    });

    // 视频画布事件
    // this.engine.addEventListener("onVideoCanvas", (direction: string, videoStreamType: string, userID: string) => {
    //   const message = `onVideoCanvas通知:direction = ${direction}, videoStreamType = ${videoStreamType}, userID = ${userID}`;
    //   console.log(message);
    //   this.triggerEvent('videoCanvas', { direction, videoStreamType, userID });

    //   if (direction === 'local') {
    //     if (videoStreamType === 'video') {
    //       //重新设置本地Video（即摄像头）的画布
    //       this.engine.setupLocalVideoCanvas({
    //         renderMode: 0, // 0表示使用视频，视频等比缩放，1表示适应区域，会裁剪，2：折中方案
    //         mirrorMode: 1, //1表示启用镜像，2表示不启用
    //         isMediaOverlay: false
    //       });
    //     }
    //   } else if (videoStreamType == 'subStreamVideo') {
    //     //重新设置本地subStramVideo(即屏幕共享)的画布
    //     this.engine.setupLocalSubStreamVideoCanvas({
    //       renderMode: 0,
    //       mirrorMode: 1
    //     });
    //   } else if (direction === 'remote') {
    //     if (videoStreamType === 'video') {
    //       //重新设置远端Video（即摄像头）的画布
    //       this.engine.setucreateEnginepRemoteVideoCanvas({
    //         renderMode: NERTCRenderMode.Fit,
    //         mirrorMode: NERTCMirrorMode.AUTO,
    //         userID
    //       });
    //     }
    //   } else if (videoStreamType == 'subStreamVideo') {
    //     //重新设置远端subStramVideo(即屏幕共享)的画布
    //     this.engine.setupRemoteSubStreamVideoCanvas({
    //       renderMode: NERTCRenderMode.Fit,
    //       mirrorMode: NERTCMirrorMode.AUTO,
    //       userID
    //     });
    //   }
    // });

    // 自己加入频道事件
    this.engine.addEventListener("onJoinChannel", (result: number, channelId: string, elapsed: number, userID: string) => {
      const message = `onJoinChannel通知:自己加入房间状况,result = ${result}, channelId = ${channelId}, elapsed = ${elapsed}, userID = ${userID}`;
      console.log(message);
      this.triggerEvent('joinChannel', { result, channelId, elapsed, userID });
    });

    // 自己离开房间
    this.engine.addEventListener("onLeaveChannel", (result: number) => {
      const message = `onLeaveChannel通知:自己离开房间状况,result = ${result}`;
      console.log(message);
      this.triggerEvent('leaveChannel', { result });
    });

    // 有人加入房间
    this.engine.addEventListener("onUserJoined", (userID: string) => {
      const message = `onUserJoined通知:有人加入房间,userID = ${userID}`;
      console.log(message);
      this.triggerEvent('userJoined', { userID });
    });

    // 离开房间
    this.engine.addEventListener("onUserLeave", (userID: string, reason: number) => {
      const message = `onUserLeave通知:有人离开房间,userID = ${userID}, reason = ${reason}`;
      console.log(message);
      this.triggerEvent('userLeave', { userID, reason });
    });

    // 对方开启音频
    this.engine.addEventListener("onUserAudioStart", (userID: string) => {
      const message = `onUserAudioStart通知:对方开启音频,userID = ${userID}`;
      console.log(message);
      this.triggerEvent('userAudioStart', { userID });
    });

    // 对方关闭音频
    this.engine.addEventListener("onUserAudioStop", (userID: string) => {
      const message = `onUserAudioStop通知:对方关闭音频,userID = ${userID}`;
      console.log(message);
      this.triggerEvent('userAudioStop', { userID });
    });

    // 对方开启视频
    this.engine.addEventListener("onUserVideoStart", (userID: string, maxProfile: number) => {
      const message = `onUserVideoStart通知:对方开启视频,userID = ${userID}, maxProfile = ${maxProfile}`;
      console.log(message);
      this.triggerEvent('userVideoStart', { userID, maxProfile });

      const remoteUserID = `${userID}`;
      if (!this.remoteUserIdVideoList.includes(remoteUserID)) {
        this.remoteUserIdVideoList.push(remoteUserID);
        this.subscribeRemoteVideo(userID);
      }
    });

    // 对方关闭视频
    this.engine.addEventListener("onUserVideoStop", (userID: string) => {
      const message = `onUserVideoStop通知：对方关闭视频，userID = ${userID}`;
      console.log(message);
      this.triggerEvent('userVideoStop', { userID });
    });
    // #endif
  }

  // 注册自定义事件监听
  on(event: string, callback: Function) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)?.push(callback);
  }

  // 触发自定义事件
  private triggerEvent(event: string, data: EventData) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event)?.forEach(callback => {
        callback(data);
      });
    }
  }

  // 移除单个事件监听
  off(event: string, callback?: Function) {
    if (!this.eventListeners.has(event)) {
      return;
    }

    if (callback) {
      // 移除特定的回调函数
      const callbacks = this.eventListeners.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index !== -1) {
          callbacks.splice(index, 1);
        }
      }
    } else {
      // 移除该事件的所有回调
      this.eventListeners.delete(event);
    }
  }

  // 移除所有事件监听
  removeAllEventListeners() {
    // #ifdef APP-PLUS
    if (this.engine) {
      this.engine.removeAllEventListener();
    }
    // #endif

    this.eventListeners.clear();
  }

  // 请求权限
  requestPermissions() {
    // #ifdef APP-PLUS
    if (uni.getSystemInfoSync().platform === "android") {
      permision.requestAndroidPermission("android.permission.RECORD_AUDIO");
      permision.requestAndroidPermission("android.permission.CAMERA");
    }
    // #endif
  }

  // 启用本地视频
  enableLocalVideo(enable: boolean) {
    if (!this.engine) return;

    // #ifdef APP-PLUS
    this.engine.enableLocalVideo({
      enable: enable,
      videoStreamType: NERtcVideoStreamType.MAIN
    });
    // #endif
  }

  // 设置本地视频画布
  setupLocalVideoCanvas(options: VideoCanvasOptions = {}) {
    if (!this.engine) return;

    // #ifdef APP-PLUS
    try {
      this.engine.setupLocalVideoCanvas({
        renderMode: options.renderMode || NERTCRenderMode.Fit,
        mirrorMode: options.mirrorMode || NERTCMirrorMode.AUTO,
        view: options.view,
        isMediaOverlay: false
      });
    } catch (error) {
      console.error('设置本地视频画布失败:', error);
      throw error;
    }
    // #endif
  }

  // 设置远程视频画布
  setupRemoteVideoCanvas(userID: string, options: VideoCanvasOptions = {}) {
    if (!this.engine) return;

    // #ifdef APP-PLUS
    try {
      this.engine.setupRemoteVideoCanvas({
        renderMode: options.renderMode || NERTCRenderMode.Fit,
        mirrorMode: options.mirrorMode || NERTCMirrorMode.AUTO,
        userID: userID,
        view: options.view || userID,
        isMediaOverlay: false
      });
    } catch (error) {
      console.error('设置远程视频画布失败:', error);
      throw error;
    }
    // #endif
  }

  // 订阅远程视频
  subscribeRemoteVideo(userID: string, streamType: number = NERtcRemoteVideoStreamType.HIGH) {
    if (!this.engine) return;

    // #ifdef APP-PLUS
    try {
      // 检查是否有 subscribeRemoteVideo 方法接受对象参数
      if (typeof this.engine.subscribeRemoteVideo === 'function') {
        try {
          this.engine.subscribeRemoteVideo({
            userID: userID,
            streamType: streamType
          });
        } catch (error) {
          // 如果对象参数调用失败，尝试直接传递参数
          console.log('尝试使用兼容方式订阅远程视频');
          this.engine.subscribeRemoteVideo(userID, streamType);
        }
      }
    } catch (error) {
      console.error('订阅远程视频失败:', error);
    }
    // #endif
  }

  // 开始预览
  startPreview() {
    if (!this.engine) return;

    // #ifdef APP-PLUS
    this.engine.startPreview(NERtcVideoStreamType.MAIN);
    // #endif
  }

  // 停止预览
  stopPreview() {
    if (!this.engine) return;

    // #ifdef APP-PLUS
    this.engine.stopPreview();
    // #endif
  }

  // 加入频道
  joinChannel(options: JoinChannelOptions) {
    if (!this.engine) return;

    // console.log('[NERTC-APP] joinChannel() 房间信息:', options);

    // #ifdef APP-PLUS
    try {
      this.engine.joinChannel({
        token: options.token,
        channelName: options.channelName,
        myUid: options.myUid,
        stringUid: options.stringUid,
        permissionKey: options.permissionKey
      });
      console.log('加入频道成功',{
        token: options.token,
        channelName: options.channelName,
        myUid: options.myUid,
        stringUid: options.stringUid,
        permissionKey: options.permissionKey
      });

    } catch (error) {
      console.error('加入频道失败:', error);
      throw error;
    }
    // #endif
  }

  // 离开频道
  leaveChannel() {
    if (!this.engine) return;

    // #ifdef APP-PLUS
    this.engine.leaveChannel();
    // #endif
  }

  // 静音本地音频
  muteLocalAudio(mute: boolean) {
    if (!this.engine) return;

    // #ifdef APP-PLUS
    this.engine.muteLocalAudio(mute);
    // #endif
  }

  // 设置扬声器状态
  setSpeakerphoneOn(on: boolean) {
    if (!this.engine) return;

    // #ifdef APP-PLUS
    this.engine.setSpeakerphoneOn(on);
    // #endif
  }

  // 切换摄像头
  switchCamera() {
    if (!this.engine) return;

    // #ifdef APP-PLUS
    this.engine.switchCamera();
    // #endif
  }

  // 本地视频预览的view组件加载完成时调用
  onLocalVideoViewLoad() {
    const message = '本端视频预览的view组件加载完成，可以设置画布';
    console.log(message);
    if (!this.engine) {
      // 由于当前Demo本地视频摄像头预览的nertc-local-view组件初始化就加载了，此时应该还没有初始化音视频SDK的引擎
      return;
    }
    // #ifdef APP-PLUS
    this.engine.nertcPrint(message);
    this.setupLocalVideoCanvas({
      renderMode: NERTCRenderMode.Fit,
      mirrorMode: NERTCMirrorMode.AUTO,
      isMediaOverlay: false
    });
    // #endif
  }

  // 销毁引擎
  destroyEngine() {
    if (!this.engine) return;

    this.removeAllEventListeners();

    // #ifdef APP-PLUS
    this.stopPreview();
    this.leaveChannel();
    this.engine.destroyEngine();
    // #endif

    this.engine = null;
    this.isSetup = false;
    this.remoteUserIdVideoList = [];
  }
}

// 创建单例
const callService = new CallService();
export default callService;
