<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>即时通讯 IM</title>
    <script>
      var coverSupport =
        'CSS' in window &&
        typeof CSS.supports === 'function' &&
        (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
          (coverSupport ? ', viewport-fit=cover' : '') +
          '" />'
      )
    </script>
    <script src="//cdn.jsdelivr.net/npm/eruda"></script>
    <script>
      function getQueryParams(url) {
        var queryParams = {}
        var queryString = url.split('?')[1]
        if (queryString) {
          var pairs = queryString.split('&')
          for (var i = 0; i < pairs.length; i++) {
            var pair = pairs[i].split('=')
            queryParams[decodeURIComponent(pair[0])] = decodeURIComponent(
              pair[1] || ''
            )
          }
        }
        return queryParams
      }

      if (getQueryParams(location.href).debug === 'true') {
        eruda.init()
      }
    </script>
    <!--preload-links-->
    <!--app-context-->
  </head>
  <body>
    <div id="app"><!--app-html--></div>
    <script type="module" src="/main.js"></script>
  </body>
</html>
