// en.js
export default {
  appText: 'NIM',
  messageText: 'Message',
  contactText: 'Contacts',
	cantactsText: "Discover",
  mineText: 'Me',
  logoutText: 'Log out',
  commsEaseText: 'About NIM',
  logoutConfirmText: 'Are you sure you want to log out?',
  avatarText: 'Avatar',
  accountText: 'Account',
  nick: 'Nickname',
  genderText: 'Gender',
  tel: 'Phone',
  email: 'Email',
  signature: 'Signature',
  man: 'Male',
  woman: 'Female',
  unknow: 'Unknown',
  birthText: 'Birthday',
  FriendPageText: 'Friend Card',
  startConversationText: 'Start Chat',
  addText: 'Add',
  applyFriendSuccessText: 'Request Sent',
  applyFriendFailText: 'Request Failed',
  addFriendText: 'Add Friend',
  addFriendFailText: 'Add Friend Failed',
  deleteFriendText: 'Delete Friend',
  deleteFriendConfirmText: 'Are you sure you want to delete this friend?',
  deleteFriendSuccessText: 'Delete Friend Success',
  deleteFriendFailText: 'Delete Friend Failed',
  setBlackFailText: 'Add to blacklist failed',
  removeBlackFailText: 'Remove from blacklist failed',
  removeBlackSuccessText: 'Remove from blacklist success',
  addPermissionText:
    'Please add relevant permissions in the system settings page',
  FailAvatarText: 'Avatar upload failed',
  chatButtonText: 'Go to Chat',
  gotoChatFailText: 'Go to chat failed',
  chatWithFriendText: 'Chat',
  okText: 'OK',
  updateText: 'Update',
  cancelText: 'Cancel',
  friendSelectText: 'Select Friend',
  telErrorText: 'Invalid phone number format',
  emailErrorText: 'Invalid email format',
  createButtonText: 'Create',
  createP2PText: 'Start Chat',
  createTeamText: 'Create Group Chat',
  addTeamMemberText: 'Add Group Member',
  addTeamMemberFailText: 'Add Group Member Failed',
  teamTitlePlaceholder: 'Enter Group Name',
  maxSelectedText: 'You can only select up to 200 friends',
  createTeamSuccessText: 'Create Group Success',
  createTeamFailedText: 'Create Group Failed',
  PersonalPageText: 'PersonalCard',
  deleteSessionText: 'Delete Conversation',
  sessionMuteText: 'Mute Notifications',
  sessionMuteFailText: 'Mute Notifications Failed',
  sessionUnMuteFailText: 'Unmute Notifications Failed',
  stickTopText: 'Pin Chat',
  deleteStickTopText: 'Unpin Chat',
  addStickTopText: 'Pin Message',
  deleteSessionFailText: 'Delete Conversation Failed',
  deleteStickTopFailText: 'Unpin Chat Failed',
  addStickTopFailText: 'Pin Message Failed',
  teamMemberText: 'Group Members',
  saveText: 'Save',
  setText: 'Settings',
  failText: 'Failed',
  saveSuccessText: 'Save Success',
  saveFailedText: 'Save Failed',
  searchTeamPlaceholder: 'Enter Group Name',
  teamTitle: 'Group Name',
  networkError: 'Current network error',
  searchFailText: 'Search Failed',
  offlineText:
    'The current network is unavailable, please check your network settings.',
  connectingText:
    'The current IM connection has been disconnected and is reconnecting…',
  teamMenuText: 'My Groups',
  teamChooseText: 'My Group Chats',
  chooseText: 'Choose',
  friendSelect: 'Please select a contact',
  getHistoryMsgFailedText: 'Failed to get message history',
  deleteText: 'Delete',
  recallText: 'Recall',
  copyText: 'Copy',
  forwardText: 'Forward',
  forwardComment: 'forward comment',
  replyText: 'Reply',
  replyNotFindText: 'This message has been recalled or deleted',
  forwardToTeamText: 'Forward to Group',
  forwardToFriendText: 'Forward to Contact',
  sessionRecordText: 'Conversation Record',
  chatInputPlaceHolder: 'What would you like to say?',
  you: 'You',
  delete: 'Delete this message',
  recall: 'Recalled a message',
  recall2: 'This message has been recalled',
  recall3: 'Recall this message',
  recallMsgFailText: 'Recall Message Failed',
  reeditText: 'Edit',
  loadingMoreText: 'Loading…',
  noMoreText: 'No more',
  deleteMsgSuccessText: 'Delete Success',
  deleteMsgFailText: 'Delete Failed',
  conversationEmptyText: 'No conversation',
  teamOwner: 'Group Owner',
  teamManager: 'Group Manager',
  teamEmptyText: 'NoGroup Chats',
  blacklistEmptyText: 'No Blocked Contacts',
  blacklistSubTitle:
    'You will not receive messages from any contact in the list',
  validEmptyText: 'No Verification Messages',
  dismissTeamText: 'Dismiss Group',
  dismissTeamConfirmText: 'Are you sure you want to dismiss this group?',
  leaveTeamTitle: 'Leave Group',
  leaveTeamConfirmText: 'Are you sure you want to leave this group?',
  leaveTeamSuccessText: 'You have left this group',
  leaveTeamFailedText: 'Failed to leave this group',
  dismissTeamSuccessText: 'Dismiss Group Success',
  dismissTeamFailedText: 'Dismiss Group Failed',
  updateTeamSuccessText: 'Update Success',
  updateTeamFailedText: 'Update Failed',
  teamTitleConfirmText: 'Group name cannot be empty',
  sendImageFailedText: 'Failed to send Image',
  sendVideoFailedText: 'Failed to send Video',
  sendAudioFailedText: 'Failed to send Audio',
  weekText: 'Weeks ago',
  dayText: 'Days ago',
  hourText: 'Hours ago',
  minuteText: 'Minutes ago',
  nowText: 'Just now',
  sendText: 'Send',
  sendToText: 'Send to',
  textMsgText: 'Text Message',
  audioMsgText: 'Audio Message',
  videoMsgText: 'Video Message',
  fileMsgText: 'File Message',
  callMsgText: 'Call Message',
  geoMsgText: 'Location Message',
  imgMsgText: 'Image Message',
  notiMsgText: 'Notification Message',
  robotMsgText: 'Robot Message',
  tipMsgText: 'Tip Message',
  customMsgText: 'Custom Message',
  unknowMsgText: 'Unknown Message',
  noFriendText: 'No Friends',
  onDismissTeamText: 'This group has been dismissed',
  onRemoveTeamText: 'You have left the group',
  selectSessionFailText: 'Failed to select conversation',
  noExistUser: 'This user does not exist',
  enterAccount: 'Enter account',
  validMsgText: 'Verification Message',
  blacklistText: 'Blocked Contacts',
  applyTeamText: 'Apply to Join Group',
  acceptResultText: 'Accepted',
  rejectResultText: 'Rejected',
  rejectTeamInviteText: 'Rejected group invitation',
  beRejectResultText: 'Rejected friend request',
  passResultText: 'Accepted friend request',
  acceptedText: 'This request has been accepted',
  acceptFailedText: 'Failed to accept this request',
  rejectedText: 'This request has been rejected',
  rejectFailedText: 'Failed to reject this request',
  passFriendAskText:
    "I have accepted your friend request, let's start chatting!",
  applyFriendText: 'Add you as a friend',
  rejectText: 'Reject',
  acceptText: 'Accept',
  acceptFriendText: 'Accept',  // 新增好友申请专用的文本
  inviteTeamText: 'Invite to Group',
  addBlacklist: 'Block',
  removeBlacklist: 'Unblock',
  forwardSuccessText: 'Forward Success',
  forwardFailText: 'Forward Failed',
  sendFailWithInBlackText:
    'The other party has blocked you, message sending failed',
  sendFailWithDeleteText:
    'The relationship between both parties has been terminated, please reapply for contact',
  friendVerificationText: 'Friend Verification',
  teamAll: 'all',
  chooseMentionText: 'Choose Mention',
  someoneText: 'someone',
  meText: 'mention',
  teamMutePlaceholder: 'The current group owner has set the group to mute',
  viewMoreText: 'View More',
  resendMsgFailText: 'Resend Failed',
  audioBtnText: 'Press and hold to speak',
  audioRemindText: 'Release to send',
  audioErrorText: 'Recording failed',
  videoPlayText: 'Video Play',
  voiceCallText: 'Voice Call',
  videoCallText: 'Video Call',
  callDurationText: 'call duration',
  callCancelText: 'canceled',
  callRejectedText: 'rejected',
  callTimeoutText: 'Missed call',
  callBusyText: 'busy line',
  callFailedText: 'call failed',
  confirmCallText: 'Confirm Call',
  confirmVideoCallText: 'Are you sure you want to start a video call?',
  confirmAudioCallText: 'Are you sure you want to start an audio call?',
  confirmText: 'Confirm',
  cancelText: 'Cancel',
  wxAppFileCopyText: 'Please paste the URL in the mobile browser.',
  shootText: 'shoot',
  albumText: 'album',
  takePhotoText: 'Take Photo',
  recordVideoText: 'Record Video',
  sendFailedText: 'Send Failed',
  // emoji 不能随便填，要用固定 key
  Laugh: '[Laugh]',
  Happy: '[Happy]',
  Sexy: '[Sexy]',
  Cool: '[Cool]',
  Mischievous: '[Mischievous]',
  Kiss: '[Kiss]',
  Spit: '[Spit]',
  Squint: '[Squint]',
  Cute: '[Cute]',
  Grimace: '[Grimace]',
  Snicker: '[Snicker]',
  Joy: '[Joy]',
  Ecstasy: '[Ecstasy]',
  Surprise: '[Surprise]',
  Tears: '[Tears]',
  Sweat: '[Sweat]',
  Angle: '[Angle]',
  Funny: '[Funny]',
  Awkward: '[Awkward]',
  Thrill: '[Thrill]',
  Cry: '[Cry]',
  Fretting: '[Fretting]',
  Terrorist: '[Terrorist]',
  Halo: '[Halo]',
  Shame: '[Shame]',
  Sleep: '[Sleep]',
  Tired: '[Tired]',
  Mask: '[Mask]',
  ok: '[ok]',
  AllRight: '[All right]',
  Despise: '[Despise]',
  Uncomfortable: '[Uncomfortable]',
  Disdain: '[Disdain]',
  ill: '[ill]',
  Mad: '[Mad]',
  Ghost: '[Ghost]',
  Angry: '[Angry]',
  Unhappy: '[Unhappy]',
  Frown: '[Frown]',
  Broken: '[Broken]',
  Beckoning: '[Beckoning]',
  Ok: '[Ok]',
  Low: '[Low]',
  Nice: '[Nice]',
  Applause: '[Applause]',
  GoodJob: '[Good job]',
  Hit: '[Hit]',
  Please: '[Please]',
  Bye: '[Bye]',
  First: '[First]',
  Fist: '[Fist]',
  GiveMeFive: '[Give me five]',
  Knife: '[Knife]',
  Hi: '[Hi]',
  No: '[No]',
  Hold: '[Hold]',
  Think: '[Think]',
  Pig: '[Pig]',
  NoListen: '[No listen]',
  NoLook: '[No look]',
  NoWords: '[No words]',
  Monkey: '[Monkey]',
  Bomb: '[Bomb]',
  Cloud: '[Cloud]',
  Rocket: '[Rocket]',
  Ambulance: '[Ambulance]',
  Poop: '[Poop]',
}
