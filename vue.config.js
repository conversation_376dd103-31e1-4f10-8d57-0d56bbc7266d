const ScriptSetup = require('unplugin-vue2-script-setup/webpack').default
module.exports = {
  parallel: false,
  configureWebpack: {
    plugins: [
      ScriptSetup({
        /* options */
        reactivityTransform: true,
        inlineTemplate: true,
        refTransform: true,
        scriptSetupOptions: {
          exportRenderFn: true,
          defineModel: true,
          defineEmits: true,
          defineProps: true,
          hoistStatic: true,
        }
      }),
    ],
  },
  chainWebpack(config) {
    // disable type check and let `vue-tsc` handles it
    config.plugins.delete('fork-ts-checker')
  },
}
