<template>
  <div
    class="msg-item-wrapper"
    :id="MSG_ID_FLAG + msg.idClient"
    :key="msg.time"
  >
    <div
      class="msg-time"
      v-if="msg.type === 'custom' && msg.attach.type === 'time'"
    >
      {{ msg.attach.value }}
    </div>
    <div
      class="msg-common"
      :style="{
        flexDirection: !isSelf ? 'row' : 'row-reverse',
      }"
      v-else-if="
        msg.type === 'custom' &&
        msg.attach.type === 'reCallMsg' &&
        msg.attach.canEdit
      "
    >
      <Avatar
        :account="msg.from"
        :teamId="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <MessageBubble :msg="msg" :bg-visible="true">
        {{ t('recall2') }}
        <text
          class="msg-recall-btn"
          @tap="
            () => {
              handleReeditMsg(msg)
            }
          "
        >
          {{ t('reeditText') }}
        </text>
      </MessageBubble>
    </div>
    <div
      class="msg-common"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
      v-else-if="
        msg.type === 'custom' &&
        msg.attach.type === 'reCallMsg' &&
        !msg.attach.canEdit
      "
    >
      <Avatar
        :account="msg.from"
        :teamId="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <MessageBubble :msg="msg" :bg-visible="true">
        <div class="recall-text">{{ t('you') + t('recall') }}</div>
      </MessageBubble>
    </div>
    <div
      class="msg-common"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
      v-else-if="msg.type === 'custom' && msg.attach.type === 'beReCallMsg'"
    >
      <Avatar
        :account="msg.from"
        :teamId="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div class="msg-name" v-if="!isSelf">{{ appellation }}</div>
        <div :class="isSelf ? 'self-msg-recall' : 'msg-recall'">
          <text class="msg-recall2">
            {{ !isSelf ? t('recall2') : `${t('you') + t('recall')}` }}</text
          >
        </div>
      </div>
    </div>
    <div
      class="msg-common"
      v-else-if="msg.type === 'text'"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
    >
      <Avatar
        :account="msg.from"
        :teamId="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div class="msg-name" v-if="!isSelf">{{ appellation }}</div>
        <MessageBubble :msg="msg" :tooltip-visible="true" :bg-visible="true">
          <ReplyMessage
            v-if="replyMsg.idClient"
            :replyMsg="replyMsg"
          ></ReplyMessage>
          <MessageText :msg="msg"></MessageText>
        </MessageBubble>
      </div>
    </div>
    <div
      class="msg-common"
      v-else-if="msg.type === 'image'"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
    >
      <Avatar
        :account="msg.from"
        :teamId="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div class="msg-name" v-if="!isSelf">{{ appellation }}</div>
        <MessageBubble
          :msg="msg"
          :tooltip-visible="true"
          :bg-visible="true"
          style="cursor: pointer"
        >
          <div
            class="image-container"
            @tap="
              () => {
                handleImageTouch(imageUrl || msg.attach.url)
              }
            "
          >
            <image
              class="msg-image"
              :lazy-load="true"
              mode="widthFix"
              :style="{ width: '100%', maxWidth: 'calc(98vw - 16px)' }"
              :src="imageUrl"
              @error="handleImageError"
            ></image>
          </div>
        </MessageBubble>
      </div>
    </div>
    <div
      class="msg-common"
      v-else-if="msg.type === 'video'"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
    >
      <Avatar
        :account="msg.from"
        :teamId="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div class="msg-name" v-if="!isSelf">{{ appellation }}</div>
        <MessageBubble
          :msg="msg"
          :tooltip-visible="true"
          :bg-visible="true"
          style="cursor: pointer"
        >
          <div class="video-msg-wrapper image-container" @tap="() => handleVideoTouch(msg)">
            <div class="video-play-button">
              <div class="video-play-icon"></div>
            </div>
            <image
              class="msg-image"
              :lazy-load="true"
              mode="widthFix"
              :style="{ width: '100%', maxWidth: 'calc(98vw - 16px)' }"
              :src="videoFirstFrameDataUrl"
              @error="handleVideoImageError"
            ></image>
          </div>
        </MessageBubble>
      </div>
    </div>
    <div
      class="msg-common"
      v-else-if="msg.type === 'g2'"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
    >
      <Avatar
        :account="msg.from"
        :teamId="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div class="msg-name" v-if="!isSelf">{{ appellation }}</div>
        <MessageBubble :msg="msg" :tooltip-visible="true" :bg-visible="true">
          <MessageG2 :msg="msg" />
        </MessageBubble>
      </div>
    </div>
    <div
      class="msg-common"
      v-else-if="msg.type === 'file'"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
    >
      <Avatar
        :account="msg.from"
        :teamId="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div class="msg-name" v-if="!isSelf">{{ appellation }}</div>
        <MessageBubble :msg="msg" :tooltip-visible="true" :bg-visible="false">
          <MessageFile :msg="msg" />
        </MessageBubble>
      </div>
    </div>
    <div
      class="msg-common"
      v-else-if="msg.type === 'audio'"
      :style="{
        flexDirection: !isSelf ? 'row' : 'row-reverse',
      }"
    >
      <Avatar
        :account="msg.from"
        :teamId="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div class="msg-name" v-if="!isSelf">{{ appellation }}</div>
        <MessageBubble
          :msg="msg"
          :tooltip-visible="true"
          :bg-visible="true"
          style="cursor: pointer"
        >
          <MessageAudio
            :msg="msg"
            :broadcastNewAudioSrc="broadcastNewAudioSrc"
          />
        </MessageBubble>
      </div>
    </div>
    <MessageNotification v-else-if="msg.type === 'notification'" :msg="msg" />
    <div
      class="msg-common"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
      v-else
    >
      <Avatar
        :account="msg.from"
        :teamId="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div class="msg-name" v-if="!isSelf">{{ appellation }}</div>
        <MessageBubble :msg="msg" :tooltip-visible="true" :bg-visible="true">
          [{{ t('unknowMsgText') }}]
        </MessageBubble>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onUnmounted, watch, defineProps } from '../../../utils/transformVue'
import Avatar from '../../../components/Avatar.vue'
import MessageBubble from './message-bubble.vue'
import { events, MSG_ID_FLAG } from '../../../utils/constants'
import { autorun } from 'mobx'
import { deepClone, stopAllAudio } from '../../../utils'
import { t } from '../../../utils/i18n'
import ReplyMessage from './message-reply.vue'
import MessageFile from './message-file.vue'
import MessageText from './message-text.vue'
import MessageAudio from './message-audio.vue'
import MessageNotification from './message-notification.vue'
import MessageG2 from './message-g2.vue'
import { customNavigateTo } from '../../../utils/customNavigate'

const props = defineProps({
  scene: {
    type: String, // Assuming TMsgScene is a custom object type
    required: true,
  },
  to: {
    type: String,
    required: true,
  },
  msg: {
    type: Object, // Assuming IMMessage is a custom object type
    default: () => ({
      idClient: undefined,
      body: undefined,
      attach: {
        type: undefined,
        value: undefined,
        url: undefined,
        canEdit: false,
        canRecall: false,
      },
      type: undefined,
      from: undefined,
      to: undefined,
      scene: undefined,
      time: undefined,
    }),
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
  replyMsg: {
    type: Object,
    default: () => ({
      idClient: undefined,
      body: undefined,
      attach: {
        type: undefined,
        value: undefined,
        url: undefined,
        canEdit: false,
        canRecall: false,
      },
      type: undefined,
    }),
  },
  broadcastNewAudioSrc: {
    type: String,
  },
})
const appellation = ref('')
const isSelf = ref(false)

// 获取视频首帧
const videoFirstFrameDataUrl = computed(() => {
  const url = props.msg?.attach?.url
  return url ? `${url}${url.includes('?') ? '&' : '?'}vframe=1` : '/static/default-img.png'
})

// 导入图片缓存工具
import { getImagePath } from '../../../utils/imageCache'

// 使用ref而不是computed，这样可以避免URL变化时立即重新渲染
// 初始化时就设置默认图片，确保加载时有占位符
const imageUrl = ref('/static/default-img.png')
// 记录上次处理的消息ID，避免重复处理
const lastProcessedMsgId = ref('')

// 监视图片消息的URL变化，更新图片URL
watch(() => {
  // 只有图片消息才需要监听
  if (props.msg?.type === 'image') {
    return {
      url: props.msg?.attach?.url || props.msg?.uploadFileInfo?.filePath,
      msgId: props.msg?.idClient || props.msg?.idServer
    }
  }
  return null
}, async (newData: { url: string; msgId: string } | null, oldData: { url: string; msgId: string } | null) => {
  // 只有图片消息且数据发生变化时才处理
  if (!newData?.url) {
    // 如果没有URL，显示默认图片
    imageUrl.value = '/static/default-img.png'
    return
  }

  // 如果是同一条消息且URL没有变化，则不处理
  if (newData.msgId === lastProcessedMsgId.value && newData.url === oldData?.url) return

  console.log(`[图片消息] 处理图片URL: msgId=${newData.msgId}, url=${newData.url.substring(0, 50)}...`)

  // 记录当前处理的消息ID
  lastProcessedMsgId.value = newData.msgId

  // 先设置默认图片作为加载时的占位符
  imageUrl.value = '/static/default-img.png'

  // 如果是本地文件路径，直接使用
  if (newData.url.startsWith('file://') || newData.url.startsWith('/')) {
    imageUrl.value = newData.url
    return
  }

  // 否则尝试从缓存获取
  try {
    // 使用缓存系统获取图片路径
    const cachedPath = await getImagePath(newData.url)
    // 只有成功获取到缓存路径且不是默认图片时才更新
    if (cachedPath && cachedPath !== '/static/default-img.png') {
      imageUrl.value = cachedPath
    }
    // 如果返回的是默认图片路径，保持当前的默认图片显示
  } catch (error) {
    console.error('加载图片缓存失败:', error)
    // 加载失败时，保持默认图片（已经设置过了）
    imageUrl.value = '/static/default-img.png'
  }
}, { immediate: true, deep: true })

const uninstallIsSelfWatch = autorun(() => {
  // @ts-ignore
  isSelf.value = props.msg.from === uni.$UIKitStore.userStore.myUserInfo.account
})

// 点击图片预览
const handleImageTouch = (clickedUrl: string) => {
  if (clickedUrl) {
    // 获取当前会话的所有消息
    // @ts-ignore
    const sessionId = `${props.scene}-${props.to}`
    // @ts-ignore
    const allMsgs = uni.$UIKitStore.msgStore.getMsg(sessionId) || []

    // 筛选出所有图片消息，按时间从新到旧排序
    const imageMessages = allMsgs
      .filter((msg: any) => msg.type === 'image' && (msg.attach?.url || msg.uploadFileInfo?.filePath))
      .sort((a: any, b: any) => b.time - a.time) // 按时间从新到旧排序

    // 提取图片URL列表，统一使用原始URL（msg.attach?.url）
    const imageUrls = imageMessages.map((msg: any) => msg.attach?.url || msg.uploadFileInfo?.filePath)

    // 获取当前消息的原始URL
    const currentOriginalUrl = props.msg?.attach?.url || props.msg?.uploadFileInfo?.filePath

    // 如果没有找到图片URL，则只预览当前图片
    if (!imageUrls.length) {
      uni.previewImage({
        urls: [currentOriginalUrl || clickedUrl],
        current: currentOriginalUrl || clickedUrl
      })
      return
    }

    // 确保当前图片的原始URL在数组中
    let currentUrl = currentOriginalUrl
    if (currentOriginalUrl && !imageUrls.includes(currentOriginalUrl)) {
      imageUrls.unshift(currentOriginalUrl)
      currentUrl = currentOriginalUrl
    } else if (!currentOriginalUrl) {
      // 如果没有原始URL，使用点击的URL
      imageUrls.unshift(clickedUrl)
      currentUrl = clickedUrl
    }

    console.log('图片预览:', {
      clickedUrl,
      currentOriginalUrl,
      currentUrl,
      imageUrls: imageUrls.slice(0, 3) // 只打印前3个用于调试
    })

    // 预览所有图片，并将当前图片设为起始图片
    uni.previewImage({
      urls: imageUrls,
      current: currentUrl
    })
  }
}

// 点击视频播放
const handleVideoTouch = (msg: any) => {
  stopAllAudio()
  const url = msg?.attach?.url
  if (url) {
    customNavigateTo({
      url: `/pages/Chat/video-play?videoUrl=${encodeURIComponent(url)}`,
    })
  }
}

// 处理图片加载错误
const handleImageError = () => {
  console.log('图片加载失败，使用默认图片')
  // 确保在加载失败时显示默认图片
  imageUrl.value = '/static/default-img.png'
}

// 处理视频封面图片加载错误
const handleVideoImageError = (event: any) => {
  console.log('视频封面图片加载失败，使用默认图片')
  event.target.src = '/static/default-img.png'
}

// 重新编辑消息
const handleReeditMsg = (msg: any) => {
  uni.$emit(events.ON_REEDIT_MSG, msg)
}

const uninstallAppellationWatch = autorun(() => {
  // 昵称展示顺序 群昵称 > 备注 > 个人昵称 > 帐号
  appellation.value = deepClone(
    // @ts-ignore
    uni.$UIKitStore.uiStore.getAppellation({
      account: props.msg.from,
      teamId: props.scene === 'team' ? props.to : '',
    })
  )
})

onUnmounted(() => {
  uninstallIsSelfWatch()
  uninstallAppellationWatch()
})
</script>

<style scoped lang="scss">
.msg-item-wrapper {
  padding: 0 15px 15px;
}

.msg-common {
  margin-top: 8px;
  display: flex;
  align-items: flex-start;
  font-size: 16px;
}

.msg-content {
  display: flex;
  flex-direction: column;
  margin-left: 8px; /* 减少左侧外边距，使图片能够显示得更大 */
  margin-right: 8px; /* 减少右侧外边距，使图片能够显示得更大 */
}

.msg-name {
  font-size: 12px;
  color: #999;
  text-align: left;
  margin-bottom: 4px;
  max-width: 300rpx;
  padding-left: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-container {
  padding: 0;
  margin: 0;
  display: inline-block;
  border-radius: 8px;
  overflow: hidden;
  max-width: calc(98vw - 16px); /* 增加到98vw并减去减少后的外边距 */
  min-width: 400rpx; /* 增加最小宽度 */
  width: auto;
}

/* 图片和视频消息的特殊样式 */
.msg-content :deep(.msg-bg) {
  background-color: transparent !important;
  padding: 0 !important;
  border: none !important;
  max-width: none !important;
  overflow: visible !important;
}

.msg-image {
  max-width: calc(98vw - 16px); /* 增加到98vw并减去减少后的外边距 */
  max-height: 90vh; /* 使用视口高度的90%，避免图片过大 */
  min-width: 400rpx; /* 增加最小宽度 */
  width: auto; /* 自适应宽度 */
  height: auto; /* 自适应高度 */
  display: block;
  border-radius: 8px;
}

.msg-time {
  margin-top: 8px;
  text-align: center;
  color: #b3b7bc;
  font-size: 12px;
}

.msg-recall-btn {
  margin-left: 5px;
  color: #1861df;
}

.msg-recall2 {
  font-size: 16px;
}

.self-msg-recall {
  max-width: 360rpx;
  overflow: hidden;
  padding: 12px 16px;
  border-radius: 8px 0px 8px 8px;
  margin-right: 8px;
  background-color: #d6e5f6;
  color: #666666;
}

.msg-recall {
  max-width: 360rpx;
  overflow: hidden;
  padding: 12px 16px;
  border-radius: 0px 8px 8px 8px;
  margin-left: 8px;
  background-color: #e8eaed;
  color: #666666;
}

.recall-text {
  color: #666666;
}

.video-play-button {
  width: 50px;
  height: 50px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  z-index: 9;
}

.video-play-icon {
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 18px solid #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-40%, -50%);
}

.video-msg-wrapper {
  box-sizing: border-box;
  position: relative;
  max-width: calc(98vw - 16px); /* 增加到98vw并减去减少后的外边距 */
  min-width: 400rpx; /* 增加最小宽度 */
  border-radius: 8px;
  overflow: hidden;
  width: auto;
}
</style>
