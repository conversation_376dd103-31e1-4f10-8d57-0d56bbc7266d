// let APIURL = "http://47.120.7.135/prod-api";
// let APIURL = "http://59.49.145.149/prod-api";
// let APIURL = "http://72eh42.natappfree.cc";
// let APIURL = "https://cyly.cloud.4008890919.com";
// let APIURL = "http://cloud-119.natapp1.cc"
// let APIURL = "https://cyly.cloud.4008890919.com";
let APIURL = "http://192.168.10.8:8081";
// const APP_KEY = "4727023efa991d31d61b3b32e819bd5b";// 原来的appkey
const APP_KEY = "9471dc5329b529d957bc475d6b531be2";// chat-older的appkey
const CLIENT_ID = '428a8310cd442757ae699df5d894f051'
const SYTETEM_TYPE = 'x-chat'
const TENANT_ID = '000000'
const USER_TYPE = 'app_user'
// 音视频通话权限密钥
const PERM_SECRET = 'a660fd5efa834d0d'

module.exports = {
	APIURL,
	APP_KEY,
	CLIENT_ID,
	SYTETEM_TYPE,
	TENANT_ID,
	USER_TYPE,
	PERM_SECRET
}
