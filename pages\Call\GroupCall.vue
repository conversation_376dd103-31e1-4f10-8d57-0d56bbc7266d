<template>
  <div class="call-page">
    <!-- 顶部状态栏 -->
    <div class="call-status-bar">
      <div class="call-status">多人通话</div>
      <div class="call-duration" v-if="callStatus === 'connected'">{{ formattedDuration }}</div>
    </div>


    <!-- 视频区域 (已连接时显示) -->
    <div class="call-video-container">
      <!-- 视频网格 (包含远程视频和本地视频) -->
      <div  class="remote-video-grid" :class="{ 'fullscreen-mode': isFullscreen }">
        <!-- 本地视频 (与远程视频样式一致) -->
        <div class="remote-video-item" :class="{ 'fullscreen-video': isFullscreen && fullscreenUserId === 'local' }"
          :style="getVideoItemStyle('local')" @tap="handleVideoTap('local')"
          v-show="!isFullscreen || fullscreenUserId === 'local'">
          <div class="remote-video-view">
            <!-- #ifdef APP-PLUS -->
            <nertc-local-view style="border: 2px solid green; width: 100%;" mediaType="video"
              @onViewLoad="onLocalVideoViewLoad" :viewID="imOptions.account">
            </nertc-local-view>
            <!-- #endif -->
          </div>
          <div class="remote-user-info">
            <div class="remote-user-name">我</div>
          </div>
        </div>


        <!-- 远程视频列表 -->
        <div v-for="user in remoteUserList" :key="user.userId" class="remote-video-item"
          :class="{ 'fullscreen-video': isFullscreen && fullscreenUserId === user.userId }"
          :style="getVideoItemStyle(user.userId)" @tap="handleVideoTap(user.userId)"
          v-show="!isFullscreen || fullscreenUserId === user.userId">
          <div class="remote-video-view">
            <!-- #ifdef APP-PLUS -->
            <NertcRemoteView v-if="user.userId" mediaType="video" @onViewLoad="() => handleRemoteViewLoad(user.userId)"
              :userID="user.userId" />
            <!-- #endif -->
          </div>
          <div class="remote-user-info">
            <div class="remote-user-name">{{ user.userName || '用户' }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 通话控制按钮 (已连接时显示) -->
    <div class="call-controls">
      <div class="call-action-row">
        <div class="call-action-btn" :class="{ 'active-btn': isMuted }" @tap="handleToggleMute">
          <Icon :type="isMuted ? 'icon-paishe' : 'icon-paishe'" :size="24"></Icon>
          <div class="btn-text">{{ t('muteText') }}</div>
        </div>

        <div class="call-action-btn" :class="{ 'active-btn': isSpeakerOn }" @tap="handleToggleSpeaker">
          <Icon :type="isSpeakerOn ? 'icon-paishe' : 'icon-paishe'" :size="24"></Icon>
          <div class="btn-text">{{ t('speakerText') }}</div>
        </div>

        <div class="call-action-btn" v-if="callType === 2" @tap="handleSwitchCamera">
          <Icon type="icon-paishe" :size="24"></Icon>
          <div class="btn-text">{{ t('switchCameraText') }}</div>
        </div>

        <div class="call-action-btn exit-btn" @tap="leaveChannel">
          <Icon type="icon-audio-call" :iconStyle="{ color: 'red' }" :size="24"></Icon>
          <div class="btn-text">退出频道</div>
        </div>
        <div class="call-action-btn exit-btn" @tap="createEngine">
          <Icon type="icon-audio-call" :iconStyle="{ color: 'red' }" :size="24"></Icon>
          <div class="btn-text">初始化</div>
        </div>
        <div class="call-action-btn exit-btn" @tap="joinChannel">
          <Icon type="icon-audio-call" :iconStyle="{ color: 'red' }" :size="24"></Icon>
          <div class="btn-text">加入频道</div>
        </div>
      </div>

    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, onMounted, onBeforeUnmount } from '../../utils/transformVue'
import Icon from '../../components/Icon.vue'
import Avatar from '../../components/Avatar.vue'
import { t } from '../../utils/i18n'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { STORAGE_KEY } from 'utils/constants'
import Config from '../../utils/config'
// 通话相关
// #ifdef APP-PLUS
import { getImToken, getPermissionKey } from '../../api/resource';
import NertcLocalView from "../../NERtcUniappSDK-JS/nertc-view/NertcLocalView";
import NertcRemoteView from "../../NERtcUniappSDK-JS/nertc-view/NertcRemoteView";
import { pluginVersion } from "@/NERtcUniappSDK-JS/lib/index";
import permision from "@/NERtcUniappSDK-JS/permission.js";
import NERTC from "@/NERtcUniappSDK-JS/lib/index";
import {
  NERTCRenderMode,
  NERTCChannelConnectionState,
  NERTCMirrorMode,
  NERtcVideoStreamType,
  NERtcVideoFrameRate,
  NERtcVideoCropMode,
  NERtcDegradationPreference,
  NERtcVideoOutputOrientationMode,
  NERtcVideoProfileType,
  NERtcRemoteVideoStreamType,
  NERTCAudioDevice,
  NERTCAudioDeviceType,
  NERTCAudioDeviceState,
  NERTCVideoDeviceState,
  NERTCConnectionType,
  NERTCErrorCode,
  NERtcAudioVolumeInfo,
  NERTCAudioProfile,
  NERTCAudioScenario,
  NERTCChannelProfile,
  NERTCUserRole,
  NERtcSubStreamContentPrefer
} from '@/NERtcUniappSDK-JS/lib/NERtcDefines';
import keycenter from "./keyCenter";
// #endif
import { APP_KEY } from "../../utils/config.js";


const callType = ref(2) // 默认为视频通话 通话类型：1-音频通话，2-视频通话
const channelId = ref('')    // 群ID/房间名
const callStatus = ref('calling') // 通话状态：calling-未连接，connected-已接通，ended-已结束
// 远程用户信息
const remoteUserList = ref<{ userId: string, userName: string, avatar?: string }[]>([])

// 房间名称
const roomName = ref('')
// 通话控制状态
const isMuted = ref(false) // 是否静音
const isSpeakerOn = ref(false) // 是否使用扬声器
const callStartTime = ref(0) // 通话开始时间
const callDuration = ref(0) // 通话时长
let timerInterval: any = null // 定时器
// 当前用户信息
const imOptions = uni.getStorageSync(STORAGE_KEY)
const isSetup = ref(false) //是否初始化引擎
// 视频全屏状态
const isFullscreen = ref(false) // 是否有视频处于全屏状态
const fullscreenUserId = ref('') // 当前全屏显示的用户ID（如果是本地视频，则为'local'）
// 截图定时器
let screenshotInterval: any = null
let isPublishingStream = ref(true)
// 引擎
const engine = ref<any>(null)

// 创建引擎
function createEngine() {
  if (isSetup.value) return;
  console.log('初始化NERTC引擎, SDK版本: ', pluginVersion);
  engine.value = NERTC.setupEngineWithContext({
    appKey: APP_KEY,
    logDir: '', // expected log directory
    logLevel: 3,
  });
  addEventListener()
  console.log('初始化引擎完成，开始设置本地视频画布');
  engine.value.nertcPrint('初始化引擎完成，开始设置本地视频画布')
  // #ifdef APP-PLUS
  // 由于当前Demo本地视频摄像头预览的nertc-local-view组件初始化就加载了，因此该组件onViewLoad事件触发是，还没有初始化音视频SDK的引擎，所以需要再初始化引擎的时候设置一下视频画布
  engine.value.setupLocalVideoCanvas(keycenter.getLocalVideoCanvasConfig())
  // #endif
  console.log('初始化引擎完成，enableLocalVideo');
  engine.value.nertcPrint('初始化引擎完成，enableLocalVideo')
  // #ifdef APP-PLUS
  //判断权限
  if (uni.getSystemInfoSync().platform === "android") {
    permision.requestAndroidPermission(
      "android.permission.RECORD_AUDIO"
    );
    permision.requestAndroidPermission(
      "android.permission.CAMERA"
    );
  }
  // #endif
  engine.value.enableLocalVideo({
    enable: isPublishingStream.value, //true表示设置启动摄像头，false表示关闭摄像头
    videoStreamType: 0 //0表示视频，1表示屏幕共享 //当前demo先使用数字，正式版本会是枚举
  })

  //设置视频编码属性
  engine.value.setLocalVideoConfig(keycenter.getVideoConfig())
  isSetup.value = true
}
function addEventListener() {
  // 引擎初始化完成后触发的回调事件
  engine.value.addEventListener("onsetupEngineWithContext", (message) => {
    const imessage = `onsetupEngineWithContext通知：message = ${message}`
    engine.value.nertcPrint(imessage)
    console.log(imessage)
  });
  //注册NERTC的事件
  engine.value.addEventListener("onError", (code, message, extraInfo) => {
    let imessage = `onError通知：code = ${code}, message = ${message}, extraInfo = ${extraInfo}`
    engine.value.nertcPrint(imessage)
    console.log(imessage)
  });
  // 警告
  engine.value.addEventListener("onWaring", (code, message, extraInfo) => {
    const imessage = `onWaring通知：code = ${code}, message = ${message}, extraInfo = ${extraInfo}`
    engine.value.nertcPrint(imessage)
    console.log(imessage)
  });
  // 通知应用程序某项服务的启用状态
  engine.value.addEventListener("onServiceEnable", (serverType, isEnable) => {
    const imessage = `onServiceEnable通知：serverType = ${serverType}, isEnable = ${isEnable}`
    engine.value.nertcPrint(imessage)
    console.log(imessage)
  });
  // 自己加入房间状态
  engine.value.addEventListener("onJoinChannel", (result, channelId, elapsed, userID, userStringID) => {
    let message = `onJoinChannel通知：自己加入房间状况，result = ${result}, channelId = ${channelId}, elapsed = ${elapsed}, userID = ${userID}, userStringID = ${userStringID}`
    engine.value.nertcPrint(message)
    console.log(message)
    if (!isPublishingStream.value)
      isPublishingStream.value = !isPublishingStream.value
    //加入房间后设置音频profile
    engine.value.setAudioProfile(keycenter.getAudioProfileConfig())
    const connectState = engine.value.getConnectionState()

    //设置采集音量
    const volume = keycenter?.getAudioVolumeConfig().captureVolume || 100
    engine.value.adjustRecordingSignalVolume(volume)


    //设置整个房间的播放音量
    const channelPlaybackVolume = keycenter.getAudioVolumeConfig().channelPlaybackVolume
    engine.value.adjustChannelPlaybackSignalVolume(channelPlaybackVolume)
    //获取链接状态
    message = `getConnectionState：获取链接状态，connectState = ${connectState}`
    engine.value.nertcPrint(message)
    console.log(message)
  });
  engine.value.addEventListener("onReconnectingStart", (result) => {
    const message = `onReconnectingStart通知：开始重连`
    engine.value.nertcPrint(message)
    console.log(message)
  });
  engine.value.addEventListener("onReJoinChannel", (result) => {
    const message = `onReJoinChannel通知：自己重新加入房间状况，result = ${result}`
    engine.value.nertcPrint(message)
    console.log(message)
  });
  engine.value.addEventListener("onDisconnect", (reason) => {
    const message = `onDisconnect通知：断开reason = ${reason}`
    engine.value.nertcPrint(message)
    console.log(message)
  });
  engine.value.addEventListener("onConnectionTypeChanged", (newConnectionType) => {
    const message = `onConnectionTypeChanged通知：newConnectionType=${newConnectionType}`
    engine.value.nertcPrint(message)
    console.log(message)
  });
  engine.value.addEventListener("onConnectionStateChanged", (state, reason) => {
    const message = `onConnectionStateChanged通知：state=${state}, reason = ${reason}`
    engine.value.nertcPrint(message)
    console.log(message)
  });
  engine.value.addEventListener("onLeaveChannel", (result) => {
    const message = `onLeaveChannel通知：自己离开房间状况，result = ${result}`
    engine.value.nertcPrint(message)
    console.log(message)
  });

  engine.value.addEventListener("onUserJoined", (userID, customInfo, userStringID) => {
    const message = `onUserJoined通知：有人加入房间，userID = ${userID}, userStringID = ${userStringID}, customInfo = ${customInfo}`
    engine.value.nertcPrint(message)
    console.log(message)
  });
}


// 加入通话频道
const joinChannel = async () => {


  if (!isSetup.value) {
    createEngine();
  }
  console.log('点击加入通话');
  try {
    // // 获取音视频通话Token，传递频道名称和有效期
    // const tokenRes = await getImToken({
    //   channelName: roomName.value,
    //   ttlSec: '36000' // 设置token有效期为10小时
    // });
    // console.log(tokenRes);

    // if (tokenRes.code !== 200 || !tokenRes.msg) {
    //   uni.showToast({
    //     title: '获取通话Token失败',
    //     icon: 'none'
    //   });
    //   return;
    // }

    // // 获取权限密钥，传递频道名称、权限密钥和有效期
    // const keyRes = await getPermissionKey({
    //   channelName: roomName.value,
    //   permSecret: Config.PERM_SECRET, // 使用配置文件中的权限密钥
    //   ttlSec: '36000', // 设置权限密钥有效期为10小时
    //   privilege: '63' // 默认权限值
    // });
    // console.log(keyRes);
    // if (keyRes.code !== 200 || !keyRes.msg) {
    //   uni.showToast({
    //     title: '获取权限密钥失败',
    //     icon: 'none'
    //   });
    //   return;
    // }

    // // 保存token和密钥
    // const rtcToken = tokenRes.msg;
    // const permissionKey = keyRes.msg;

    callStatus.value = 'connected';
    // console.log('加入房间信息 :', {
    //   token: rtcToken, // 使用新获取的token
    //   channelName: roomName.value,
    //   myUid: parseInt(imOptions.account),
    //   myStringUid: imOptions.account,
    //   permissionKey: permissionKey
    // });

    engine.value.joinChannel({
      // token: rtcToken, // 使用新获取的token
      token: '',
      channelName: roomName.value,
      myUid: parseInt(imOptions.account),
      myStringUid: imOptions.account,
      // permissionKey: permissionKey
    });
  } catch (error) {
    console.error('加入频道失败:', error);
    uni.showToast({
      title: '加入通话失败',
      icon: 'none'
    });
  }
}

const onLocalVideoViewLoad = () => {
  const message = '本端视频预览的view组件加载完成，可以设置画布'
  console.log(message)
  if (!engine.value) {
    //由于当前Demo本地视频摄像头预览的nertc-local-view组件初始化就加载了，此时应该还没有初始化音视频SDK的引擎
    return
  }
  engine.value.nertcPrint(message)
  engine.value.setupLocalVideoCanvas(keycenter.getLocalVideoCanvasConfig())
}

const leaveChannel = () => {
  if (isSetup.value) {
    engine.value.leaveChannel();
  }

  callStatus.value = 'ended';
  stopCallTimer();
  uni.navigateBack();
}
// 页面加载时获取参数
onLoad((options: any = {}) => {
  console.log('GroupCall页面加载,参数:', options);
  if (options.type) {
    callType.value = parseInt(options.type)
  }
  if (options.channelId) {
    channelId.value = options.channelId
  }
  if (options.status) {
    callStatus.value = options.status
  }

  // 使用channelId作为房间名
  roomName.value = channelId.value
})

// 这个方法已经不需要了，因为我们使用了 callService.addEventListener() 和 registerCallServiceEvents()

// 处理本地视图加载完成
const handleLocalViewLoad = () => {
  console.log('本地视图加载完成');
  if (isSetup.value && callType.value === 2) {
    try {
      engine.value.setupLocalVideoCanvas({
        view: imOptions.account, // 添加view参数，对应NertcLocalView的viewID
        isMediaOverlay: false
      });
      // 开始预览
      engine.value.startPreview();
    } catch (error) {
      console.error('设置本地视频失败:', error);
    }
  }
}

// 处理远程视图加载完成
const handleRemoteViewLoad = (userId: string) => {
  console.log('远程视图加载完成，userId:', userId);
  if (isSetup.value && callType.value === 2 && userId) {
    try {
      // 确保 view 参数与 NertcRemoteView 的 userID 属性匹配
      engine.value.setupRemoteVideoCanvas(userId, {
        view: userId,
        isMediaOverlay: false
      });

      // 添加延迟，确保视图已经准备好
      setTimeout(() => {
        try {
          // 订阅远程视频
          engine.value.subscribeRemoteVideo(userId);
        } catch (error) {
          console.error('订阅远程视频失败:', error);
        }
      }, 100);
    } catch (error) {
      console.error('设置远程视频失败:', error);
    }
  }
}

// 处理静音切换
const handleToggleMute = () => {
  isMuted.value = !isMuted.value;
  if (isSetup.value) {
    engine.value.muteLocalAudio(isMuted.value);
    uni.showToast({
      title: isMuted.value ? '已静音' : '已取消静音',
      icon: 'none'
    });
  }
}

// 处理扬声器切换
const handleToggleSpeaker = () => {
  isSpeakerOn.value = !isSpeakerOn.value;
  if (isSetup.value) {
    engine.value.setSpeakerphoneOn(isSpeakerOn.value);
    uni.showToast({
      title: isSpeakerOn.value ? '已切换到扬声器' : '已切换到听筒',
      icon: 'none'
    });
  }
}

// 处理摄像头切换
const handleSwitchCamera = () => {
  if (isSetup.value) {
    engine.value.switchCamera();
    uni.showToast({
      title: t('cameraSwitchedText'),
      icon: 'none'
    });
  }
}

// 停止定时截图
const stopScreenshotTimer = () => {
  if (screenshotInterval) {
    clearInterval(screenshotInterval);
    screenshotInterval = null;
  }
}

// 在通话开始时启动截图 - 暂时注释掉，因为需要重新实现
onMounted(() => {
  // 截图功能暂时不启用
  // startScreenshotTimer();
})

// 在通话结束时停止截图
onBeforeUnmount(() => {
  stopScreenshotTimer();
})

// 计算属性：格式化通话时长
const formattedDuration = computed(() => {
  const seconds = callDuration.value
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60

  const formattedMinutes = minutes < 10 ? `0${minutes}` : `${minutes}`
  const formattedSeconds = remainingSeconds < 10 ? `0${remainingSeconds}` : `${remainingSeconds}`

  return `${formattedMinutes}:${formattedSeconds}`
})

// 获取视频元素样式
const getVideoItemStyle = (userId: string) => {
  // 如果是全屏模式且当前用户是全屏显示的用户
  if (isFullscreen.value && fullscreenUserId.value === userId) {
    return { width: '100%', height: '100%' }
  }

  // 如果是全屏模式但当前用户不是全屏显示的用户，则隐藏
  if (isFullscreen.value && fullscreenUserId.value !== userId) {
    return { display: 'none' }
  }

  // 非全屏模式下，根据参与者数量返回不同的样式
  const totalParticipants = remoteUserList.value.length + 1
  // return { width: '50%', height: '50%' }
  if (totalParticipants <= 1) {
    return { width: '100%', height: '100%' }
  } else if (totalParticipants <= 2) {
    return { width: '50%', height: '50%' }
  } else if (totalParticipants <= 4) {
    return { width: '50%', height: '50%' }
  } else {
    return { width: '33.33%', height: '33.33%' }
  }
}

// 处理视频点击事件
const handleVideoTap = (userId: string) => {
  // 如果已经是全屏状态，点击同一个视频则退出全屏
  if (isFullscreen.value && fullscreenUserId.value === userId) {
    exitFullscreen()
    return
  }

  // 进入全屏状态
  isFullscreen.value = true
  fullscreenUserId.value = userId
}

// 退出全屏状态
const exitFullscreen = () => {
  isFullscreen.value = false
  fullscreenUserId.value = ''
}

// 开始计时器
const startCallTimer = () => {
  callStartTime.value = Math.floor(Date.now() / 1000);
  timerInterval = setInterval(() => {
    const now = Math.floor(Date.now() / 1000);
    callDuration.value = now - callStartTime.value;
  }, 1000);
}

// 停止计时器
const stopCallTimer = () => {
  if (timerInterval) {
    clearInterval(timerInterval);
    timerInterval = null;
  }
}

onUnload(() => {
  // 清理引擎资源
  if (isSetup.value) {
    engine.value.destroyEngine();
    isSetup.value = false;
  }

  stopCallTimer();
  stopScreenshotTimer();
})
</script>

<style lang="scss" scoped>
.call-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #1a1a1a;
  color: #ffffff;
}

.call-status-bar {
  width: 100%;
  padding: 40px 0 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;

  .call-status {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .call-duration {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }
}

// 未连接时的设置界面
.call-setup {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 60px;
  z-index: 5;
  width: 80%;

  .call-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 16px;
    border: 2px solid rgba(255, 255, 255, 0.2);

    .avatar-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .call-username {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 30px;
  }

  .room-input-container {
    width: 100%;
    margin-bottom: 40px;

    .room-input {
      width: 100%;
      height: 50px;
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      color: #ffffff;
      padding: 0 15px;
      font-size: 16px;
    }
  }

  .room-info {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 30px;
    padding: 10px 20px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
  }

  .join-btn {
    background-color: #34c759;
    width: 180px;
    height: 60px;
    border-radius: 30px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 50px;

    .btn-text {
      font-size: 16px;
      margin-left: 10px;
      color: #fff;
    }
  }

  .exit-btn {
    background-color: #ff3b30;
    width: 60px;
    height: 60px;
    border-radius: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .btn-text {
      font-size: 12px;
      margin-top: 8px;
      color: #fff;
    }
  }
}

.call-video-container {
  position: absolute;
  top: 80px;
  left: 0;
  width: 100%;
  height: 70vh;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  // 全屏模式下的返回按钮
  .fullscreen-back-btn {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 36px;
    height: 36px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    color: #fff;
  }

  .remote-video-grid {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    padding: 10px;
    max-height: 66.67vh;

    // 全屏模式下的网格样式
    &.fullscreen-mode {
      padding: 0;
    }

    .remote-video-item {
      position: relative;
      padding: 5px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      // 全屏模式下的视频样式
      &.fullscreen-video {
        padding: 0;
        width: 100% !important;
        height: 100% !important;
        z-index: 5;
      }

      .remote-video-view {
        width: 100%;
        height: 100%;
        max-height: 400px;
        background-color: #333;
        display: flex;
        overflow: hidden;
        border-radius: 8px;
      }

      // 全屏模式下移除最大高度限制
      &.fullscreen-video .remote-video-view {
        max-height: none;
        border-radius: 0;
      }

      .remote-user-info {
        position: absolute;
        bottom: 10px;
        left: 10px;
        display: flex;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 4px 8px;
        border-radius: 4px;
        z-index: 6;

        .remote-user-name {

          font-size: 12px;
        }
      }
    }
  }
}

.call-controls {
  position: absolute;
  bottom: 20px;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;

  .call-action-row {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
    width: 100%;
  }

  .call-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 20px;

    .btn-text {
      font-size: 12px;
      margin-top: 8px;
      color: rgba(255, 255, 255, 0.8);
    }

    &.active-btn {
      color: #0089ff;

      .btn-text {
        color: #0089ff;
      }
    }

    &.decline-btn,
    &.hangup-btn {
      background-color: #ff3b30;
      width: 60px;
      height: 60px;
      border-radius: 30px;
      justify-content: center;

      .btn-text {
        color: #fff;
      }
    }

    &.accept-btn {
      background-color: #34c759;
      width: 60px;
      height: 60px;
      border-radius: 30px;
      justify-content: center;

      .btn-text {
        color: #fff;
      }
    }
  }
}
</style>
