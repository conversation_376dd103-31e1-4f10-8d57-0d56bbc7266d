<template>
	<view class="page-container">
		<!-- 标题 -->
		<view class="page-title">
			<text class="title-text">请填写老人信息</text>
			<view class="title-underline"></view>
		</view>

		<!-- 表单内容 -->
		<view class="form-content">
			<!-- 老人信息部分 -->
			<view class="section-title">老人信息</view>

			<view class="form-group">
				<view class="form-row">
					<view class="form-label">老人名字</view>
					<input class="form-input" focus placeholder="请输入" v-model="elderName" />
				</view>
				<view class="form-row">
					<view class="form-label">老人身份证号</view>
					<input class="form-input" placeholder="请输入" v-model="elderIdCard" />
				</view>
			</view>

			<!-- 家属信息部分 -->
			<view class="section-title">家属信息</view>

			<view class="form-group">
				<!-- 关系选择 -->
				<view class="relation-title">您与老人的关系是？</view>
				<view class="relation-options">
					<view class="relation-option" v-for="(item, index) in guanxi" :key="index"
						:class="{ 'relation-option-selected': selectedIndex === index }" @click="selectItem(index)">
						<text>{{ item }}</text>
						<text v-if="selectedIndex === index" class="selected-icon">✓</text>
					</view>
				</view>

				<!-- 家属信息输入 -->
				<view class="form-row">
					<view class="form-label">家属姓名</view>
					<input class="form-input" placeholder="请输入" v-model="kinName" />
				</view>
				<view class="form-row">
					<view class="form-label">家属身份证号</view>
					<input class="form-input" placeholder="请输入" v-model="kinIdCard" />
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-container">
			<button class="submit-button" :loading="istrue" @click="nurseBangdings">提交</button>
		</view>
	</view>
</template>

<script>
	import {
		addUserElder,
		getImUserInfo
	} from "@/api/index"
	// import common from "@/utils/common.js" // 暂时不需要使用
	export default {
		data() {
			return {
				elderName: "",
				elderIdCard: "",
				kinName: "",
				kinIdCard: "",
				kinType: "", // 字符串类型的关系类型，对应后端需要的值
				istrue: false,
				guanxi: ['我的父母', '我的子女', '我的伴侣', '其他'],
				selectedIndex: null, // 选中的索引，用于界面显示
			}
		},
		onLoad() {
   		 // 检查是否已绑定身份
		if (this.$common.checkIdentityBound()) {
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		methods: {
			// 验证身份证号
			validateIdCard(idCard) {
				// 身份证号正则表达式（支持18位和15位）
				const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
				return idCardReg.test(idCard);
			},

			// 表单验证
			validateForm() {
				// 验证老人名字
				if (!this.elderName || this.elderName.trim() === '') {
					uni.showToast({
						title: '请输入老人名字',
						icon: 'none',
						duration: 2000
					});
					return false;
				}

				// 验证老人身份证号
				if (!this.elderIdCard || !this.validateIdCard(this.elderIdCard)) {
					uni.showToast({
						title: '请输入正确的老人身份证号',
						icon: 'none',
						duration: 2000
					});
					return false;
				}

				// 验证关系选择
				if (this.selectedIndex === null || this.kinType === "") {
					uni.showToast({
						title: '请选择与老人的关系',
						icon: 'none',
						duration: 2000
					});
					return false;
				}

				// 验证家属姓名
				if (!this.kinName || this.kinName.trim() === '') {
					uni.showToast({
						title: '请输入家属姓名',
						icon: 'none',
						duration: 2000
					});
					return false;
				}

				// 验证家属身份证号
				if (!this.kinIdCard || !this.validateIdCard(this.kinIdCard)) {
					uni.showToast({
						title: '请输入正确的家属身份证号',
						icon: 'none',
						duration: 2000
					});
					return false;
				}

				return true;
			},
			// 提交表单
			async nurseBangdings() {
				// 验证表单
				if (!this.validateForm()) {
					return;
				}
				// 设置加载状态
				this.istrue = true;
				try {
				// 准备提交数据
				// 确保所有字段都是字符串类型，并且去除空格
				let elderInfo = {
					elderName: this.elderName.trim(),
					elderIdCard: this.elderIdCard.trim(),
					kinName: this.kinName.trim(),
					kinIdCard: this.kinIdCard.trim(),
					kinType: String(this.kinType).trim(), // 确保是字符串类型
				}
					// 打印请求数据，帮助调试
					console.log('发送绑定请求，数据:', JSON.stringify(elderInfo));
					let res = await addUserElder(elderInfo);
					console.log('绑定请求响应:', res);
					// 重置加载状态
					this.istrue = false;
					if (res.code == 200) {
						uni.showToast({
							title: res.msg || '绑定成功',
							icon: 'success',
							duration: 2000
						});
					}
					await this.$common.updateUserInfoAfterBinding();
					// 绑定成功后跳转
					setTimeout(() => {
					uni.switchTab({ url: "/pages/Conversation/index" });
					}, 2000);
				} catch (error) {
					// 重置加载状态
					this.istrue = false;
					// 详细记录错误信息
					console.error('提交表单失败:', error);
					console.error('请求数据:', JSON.stringify(elderInfo));
					// 检查是否是非法请求错误
					if (error && error.message && error.message.includes('非法请求')) {
						console.error('检测到非法请求错误');
						uni.showToast({
							title: '请求路径错误，请联系管理员',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					// 错误提示
					uni.showToast({
						title: error.message || '服务器异常，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				}
				
				// 调用API
				// addUserElder(elderInfo).then(res => {
				// 	// 打印响应数据
				// 	console.log('绑定请求响应:', res);

				// 	// 重置加载状态
				// 	this.istrue = false;
				// 	 if (res.code == 200) {
				// 		// 成功提示
				// 		uni.showToast({
				// 			title: res.msg || '绑定成功',
				// 			icon: 'success',
				// 			duration: 2000
				// 		});

				// 		// 获取用户信息并跳转
				// 		getImUserInfo().then(data => {
				// 			uni.setStorageSync('userInfo', data.data)
				// 			setTimeout(() => {
				// 				uni.switchTab({ url: "/pages/Conversation/index" })
				// 			}, 2000)
				// 		}).catch(err => {
				// 			console.error('获取用户信息失败', err);
				// 			// 即使获取用户信息失败也跳转
				// 			setTimeout(() => {
				// 				uni.switchTab({ url: "/pages/Conversation/index" })
				// 			}, 2000)
				// 		});
				// 	} else {
				// 		// 记录错误信息
				// 		console.error('请求返回错误状态码:', res.code);
				// 		console.error('错误信息:', res.msg || '未知错误');

				// 		// 错误提示
				// 		uni.showToast({
				// 			title: res.msg || '信息填写错误，请检查',
				// 			icon: 'none',
				// 			duration: 2000
				// 		});
				// 	}
				// }).catch(error => {
				// 	// 重置加载状态
				// 	this.istrue = false;

				// 	// 详细记录错误信息
				// 	console.error('提交表单失败:', error);
				// 	console.error('请求数据:', JSON.stringify(elderInfo));

				// 	// 检查是否是非法请求错误
				// 	if (error && error.message && error.message.includes('非法请求')) {
				// 		console.error('检测到非法请求错误');
				// 		uni.showToast({
				// 			title: '请求路径错误，请联系管理员',
				// 			icon: 'none',
				// 			duration: 2000
				// 		});
				// 		return;
				// 	}

				// 	// 检查是否是token过期
				// 	if (error && error.code == 401) {
				// 		// 清除token
				// 		uni.removeStorageSync('token');
				// 		// 显示token过期弹窗
				// 		this.handleTokenExpired();
				// 	} else {
				// 		// 错误提示
				// 		uni.showToast({
				// 			title: error.message || '服务器异常，请稍后再试',
				// 			icon: 'none',
				// 			duration: 2000
				// 		});
				// 	}
				// })
			},

			selectItem(index) {
				this.selectedIndex = index;
				// 根据索引设置关系类型，使用字符串值
				switch(index) {
					case 0: // 我的父母
						this.kinType = "1";
						break;
					case 1: // 我的子女
						this.kinType = "2";
						break;
					case 2: // 我的伴侣
						this.kinType = "3";
						break;
					case 3: // 其他
						this.kinType = "4";
						break;
					default:
						this.kinType = "";
				}
				console.log('选择的关系类型:', this.kinType);
			},
		}
	}
</script>

<style lang="scss">
/* 使用相对单位和响应式设计 */
.page-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 0 4%;
  box-sizing: border-box;
}

.page-title {
  text-align: center;
  padding: 4% 0 2%;

  .title-text {
    font-size: 1rem;
    font-weight: 500;
    color: #00a57a;
  }

  .title-underline {
    width: 40px;
    height: 2px;
    background-color: #00a57a;
    margin: 0.4rem auto 0;
  }
}

.form-content {
  width: 100%;
  max-width: 800px; /* 平板上限制最大宽度 */
  margin: 0 auto;
}

.section-title {
  font-size: 0.95rem;
  font-weight: 500;
  color: #333;
  margin: 1rem 0 0.6rem;
  padding-bottom: 0.3rem;
  border-bottom: 1px solid #f0f0f0;
}

.form-group {
  margin-bottom: 0.8rem;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.8rem;

  .form-label {
    width: 35%;
    font-size: 0.9rem;
    color: #333;
    font-weight: 400;
    padding-right: 0.5rem;
    box-sizing: border-box;
  }

  .form-input {
    flex: 1;
    height: 2.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 0 0.8rem;
    font-size: 0.9rem;
    color: #333;
    background-color: #fff;
    box-sizing: border-box;

    &:focus {
      border-color: #00a57a;
      outline: none;
    }
  }
}

.relation-title {
  font-size: 0.9rem;
  font-weight: 400;
  color: #333;
  margin-bottom: 0.6rem;
}

.relation-options {
  display: flex;
  flex-wrap: wrap;
  gap: 2%;
  margin-bottom: 1rem;

  .relation-option {
    flex: 1;
    min-width: 22%;
    height: 2.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    border: 2px solid transparent;
    transition: all 0.2s ease-in-out;

    text {
      font-size: 0.9rem;
      color: #666;
    }

    /* 添加弹性布局，使文本和图标能够平衡显示 */
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    &:active {
      background-color: #f0f0f0;
    }

    &.relation-option-selected {
      background-color: #e1f3ff;
      border: 2px solid #0099ff;
      box-shadow: 0 2px 4px rgba(0, 153, 255, 0.2);

      text {
        color: #0099ff;
        font-weight: 600;
      }

      .selected-icon {
        margin-left: 5px;
        font-size: 0.9rem;
        color: #0099ff;
        font-weight: bold;
      }
    }
  }
}

.submit-container {
  width: 100%;
  max-width: 800px;
  margin: 1.5rem auto 0;
  padding-bottom: 1.5rem;

  .submit-button {
    width: 100%;
    height: 2.8rem;
    background-color: #00a57a;
    border-radius: 1.4rem;
    color: white;
    font-size: 1rem;
    font-weight: 500;
    border: none;

    &:active {
      opacity: 0.9;
    }
  }
}

/* 适配小屏幕手机 */
@media screen and (max-width: 360px) {
  .page-container {
    padding: 0 3%;
  }

  .page-title .title-text {
    font-size: 0.95rem;
  }

  .section-title {
    font-size: 0.9rem;
  }

  .form-row {
    margin-bottom: 0.7rem;

    .form-label {
      font-size: 0.85rem;
    }

    .form-input {
      height: 2.3rem;
      font-size: 0.85rem;
      padding: 0 0.6rem;
    }
  }

  .relation-title {
    font-size: 0.85rem;
  }

  .relation-options {
    gap: 1.5%;

    .relation-option {
      height: 2.1rem;

      text {
        font-size: 0.85rem;
      }
    }
  }

  .submit-button {
    height: 2.5rem;
    font-size: 0.95rem;
  }
}

/* 适配平板设备 */
@media screen and (min-width: 768px) {
  .page-container {
    padding: 0 6%;
  }

  .page-title {
    padding: 2% 0;

    .title-text {
      font-size: 1.2rem;
    }

    .title-underline {
      width: 50px;
      margin-top: 0.5rem;
    }
  }

  .form-content {
    padding: 0 2%;
  }

  .section-title {
    font-size: 1.1rem;
    margin: 1.5rem 0 1rem;
  }

  .form-row {
    margin-bottom: 1.2rem;

    .form-label {
      font-size: 1rem;
      width: 30%;
    }

    .form-input {
      height: 2.8rem;
      font-size: 1rem;
      padding: 0 1rem;
    }
  }

  .relation-title {
    font-size: 1rem;
    margin-bottom: 0.8rem;
  }

  .relation-options {
    gap: 2.5%;

    .relation-option {
      height: 2.6rem;
      min-width: 20%;

      text {
        font-size: 1rem;
      }
    }
  }

  .submit-container {
    margin-top: 2rem;

    .submit-button {
      width: 60%;
      height: 3rem;
      margin: 0 auto;
      font-size: 1.1rem;
    }
  }
}
</style>
