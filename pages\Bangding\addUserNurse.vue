<template>
	<view class="page-container">
		<!-- 背景图片层 -->
		<view class="background-layer">
			<image class="bg-image" src="../../static/bangding/elderly-care-bg.png" mode="aspectFill"></image>
			<view class="bg-overlay"></view>
		</view>

		<!-- 内容层 -->
		<view class="content-layer">
			<view class="header">
				<view class="header-title">请填写护理员信息</view>
			</view>

			<view class="form-container">
				<view class="form-item">
					<view class="form-label">护理员名字</view>
					<input class="form-input" focus placeholder="请输入" v-model="nurseName" />
				</view>

				<view class="form-item">
					<view class="form-label">护理员身份证号</view>
					<input class="form-input" placeholder="请输入" v-model="nurseIdCard" />
				</view>
			</view>

			<view class="submit-container">
				<button class="submit-button" :loading="istrue" @click="nurseBangdings">提交</button>
			</view>
		</view>
	</view>
</template>

<script>
import { addUserNurse, getImUserInfo } from "@/api/index"
import common from "@/utils/common.js"
export default {
	data() {
		return {
			nurseName: "",
			nurseIdCard: "",
			istrue: false
		}
	},
	onLoad() {
		// 检查是否已绑定身份
		if (this.$common.checkIdentityBound()) {
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	},
	methods: {
		// nurseBangdings() {
		// 	let nurseInfo = {
		// 		nurseName: this.nurseName,
		// 		nurseIdCard: this.nurseIdCard,
		// 	}
		// 	const userInfo = uni.getStorageSync('userInfo')
		// 	addUserNurse(nurseInfo).then(res => {
		// 		if (res.code == 200) {
		// 			uni.showToast({
		// 				title: res.msg,
		// 				icon: 'success',
		// 				duration: 2000
		// 			});
		// 			if (!userInfo) {
		// 				uni.showToast({ title: '用户信息获取失败', icon: 'none' });
		// 				return;
		// 			}
		// 			//增加护理员成功后 刷新用户信息并跳转到主页
		// 			getImUserInfo(userInfo.accid).then(data => {
		// 				console.log(data);
		// 				if (res.code == 200) {
		// 					uni.setStorageSync('userInfo', data.data)
		// 					setTimeout(() => {
		// 						uni.switchTab({ url: "/pages/Conversation/index" })
		// 					}, 2000)
		// 				}
		// 			})
		// 		} else {
		// 			uni.showToast({
		// 				title: res.msg,
		// 				icon: 'none',
		// 				duration: 2000
		// 			});
		// 		}
		// 	}).catch(error => {
		// 		uni.showToast({
		// 				title: '服务器异常',
		// 				icon: 'none',
		// 				duration: 2000
		// 			});
		// 	})
		// }
		async nurseBangdings() {
			this.istrue = true;
			try {
				const nurseInfo = {
					nurseName: this.nurseName,
					nurseIdCard: this.nurseIdCard
				};
				const { code, msg } = await addUserNurse(nurseInfo);
				this.istrue = false;
				if (code !== 200) {
					uni.showToast({ title: msg, icon: 'none' });
					return;
				}
				uni.showToast({ title: msg, icon: 'success' });
				// 更新用户数据并且存到本地
				await this.$common.updateUserInfoAfterBinding();
				setTimeout(() => {
					uni.switchTab({ url: "/pages/Conversation/index" });
				}, 2000);
			} catch (error) {
				this.istrue = false;
				console.error('绑定失败:', error);
				uni.showToast({ title: '服务器异常', icon: 'none' });
			} finally {
				this.istrue = false;
			}
		}
	}
}
</script>

<style lang='less'>
/* 页面容器 */
.page-container {
	min-height: 100vh;
	position: relative;
	display: flex;
	flex-direction: column;
}

/* 背景图片层 */
.background-layer {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
	overflow: hidden;
}

.bg-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transform: scale(1.02);
	/* 设置缩放比例，避免虚化时出现边缘 */
	filter: blur(2px);
	/* 轻微模糊效果 */
	-webkit-filter: blur(2px);
}

.bg-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg,
			rgba(255, 255, 255, 0.4) 0%,
			rgba(255, 255, 255, 0.6) 50%,
			rgba(255, 255, 255, 0.5) 100%);
	z-index: 2;
}

/* 内容层 */
.content-layer {
	position: relative;
	z-index: 3;
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	padding-bottom: 30px;
}

/* 头部样式 */
.header {
	padding: 20px 16px;
	border-bottom: 1px solid rgba(234, 234, 234, 0.8);
}

.header-title {
	font-size: 20px;
	color: #07c160;
	font-weight: 600;
	text-align: center;
	padding: 10px 0;
	position: relative;
	text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	letter-spacing: 1px;
}

.header-title::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 60px;
	height: 3px;
	background: linear-gradient(to right, #07c160, #10b981);
	border-radius: 3px;
}

/* 表单容器 */
.form-container {
	background-color: rgba(255, 255, 255, 0.75);
	margin: 20px 16px 0;
	padding: 15px 20px;
	border-radius: 16px;
	box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	border: 1px solid rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(1px);
	-webkit-backdrop-filter: blur(1px);
}

/* 表单项 */
.form-item {
	display: flex;
	flex-direction: column;
	padding: 16px 0;
	border-bottom: 1px solid rgba(240, 240, 240, 0.8);
}

.form-item:last-child {
	border-bottom: none;
}

.form-label {
	font-size: 16px;
	color: #333;
	font-weight: 500;
	margin-bottom: 10px;
}

.form-input {
	height: 44px;
	font-size: 15px;
	color: #333;
	background-color: rgba(255, 255, 255, 0.5);
	border-radius: 8px;
	padding: 0 15px;
	border: 1px solid rgba(255, 255, 255, 0.9);
	transition: all 0.3s ease;
}

.form-input:focus {
	background-color: rgba(255, 255, 255, 0.9);
	border-color: rgba(7, 193, 96, 0.5);
	box-shadow: 0 0 0 2px rgba(7, 193, 96, 0.1);
}

/* 提交按钮容器 */
.submit-container {
	padding: 24px 16px;
	margin-top: auto;
}

/* 提交按钮 */
.submit-button {
	height: 50px;
	line-height: 50px;
	background: linear-gradient(to right, #07c160, #10b981);
	color: #ffffff;
	font-size: 16px;
	font-weight: 500;
	border-radius: 25px;
	box-shadow: 0 8px 20px rgba(7, 193, 96, 0.3);
	transition: all 0.3s ease;
	border: none;
	letter-spacing: 4px;
	position: relative;
	overflow: hidden;
}

.submit-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: 0.5s;
}

.submit-button:active {
	background: linear-gradient(to right, #06ad56, #0ea875);
	transform: scale(0.98);
	box-shadow: 0 3px 8px rgba(7, 193, 96, 0.3);
}

.submit-button:active::before {
	left: 100%;
}

/* 适配小屏幕手机 */
@media screen and (max-width: 320px) {
	.form-label {
		font-size: 14px;
	}

	.form-input {
		height: 40px;
	}

	.submit-button {
		height: 44px;
		line-height: 44px;
	}
}
</style>
