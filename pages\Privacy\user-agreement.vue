<template>
    <div class="agreement-page">
        <NavBar :title="'用户服务协议'" />
        <div class="agreement-content">
            <h1 class="agreement-title">用户服务协议</h1>
            <p class="agreement-date">最后更新日期：{{ updateDate }}</p>
            <p class="agreement-date">生效日期：{{ effectiveDate }}</p>

            <div class="agreement-section">
                <h2>一、协议的接受与修改</h2>
                <p>1.1 您在使用本应用（以下简称"本服务"）前，请仔细阅读本协议。点击"同意"即视为您已充分理解并接受全部条款。</p>
                <p>1.2 我们保留修改本协议的权利，重大变更将通过应用内公告或注册手机号通知。若您继续使用服务，视为接受修订后的协议。</p>
            </div>

            <div class="agreement-section">
                <h2>二、服务内容</h2>
                <p>2.1 本服务提供以下核心功能：</p>
                <ul>
                    <li>即时通讯服务</li>
                    <li>音视频通话功能</li>
                    <li>通讯录管理</li>
                    <li>消息推送服务</li>
                </ul>
            </div>

            <div class="agreement-section">
                <h2>三、账户管理</h2>
                <p>3.1 注册要求：</p>
                <ul>
                    <li>需提供有效中国大陆手机号</li>
                    <li>年龄须满18周岁或获得监护人同意</li>
                </ul>
                <p>3.2 账户安全：</p>
                <ul>
                    <li>不得转让、出租账户</li>
                    <li>发现异常登录应立即联系客服冻结账户</li>
                </ul>
            </div>

            <div class="agreement-section">
                <h2>四、使用规范</h2>
                <p>4.1 您承诺不从事以下行为：</p>
                <ul>
                    <li>发布违法或不当信息</li>
                    <li>利用系统漏洞获取不当利益</li>
                    <li>对服务器进行恶意攻击</li>
                    <li>传播违法或侵权信息</li>
                </ul>
            </div>

            <div class="agreement-section">
                <h2>五、知识产权</h2>
                <p>5.1 本服务所有内容（包括但不限于软件代码、界面设计、通讯协议）的著作权归我们所有。</p>
                <p>5.2 用户生成的内容（聊天记录、上传资料）版权归属用户，但授予我们非独占的使用权用于服务优化。</p>
            </div>

            <div class="agreement-section">
                <h2>六、免责声明</h2>
                <p>6.1 在下列情形下，我们不承担法律责任：</p>
                <ul>
                    <li>因不可抗力（如自然灾害、政策调整）导致的服务中断</li>
                    <li>第三方服务（如网易云信、推送服务）的故障</li>
                    <li>用户未按操作指引使用服务导致的问题</li>
                </ul>
            </div>

            <div class="agreement-section">
                <h2>七、协议终止</h2>
                <p>7.1 您可通过【设置-账户安全】申请注销账户，我们将于15个工作日内完成数据处理。</p>
                <p>7.2 我们有权对违反本协议的用户采取以下措施：</p>
                <ul>
                    <li>暂停部分或全部服务</li>
                    <li>永久封禁账户</li>
                    <li>追究法律责任</li>
                </ul>
            </div>

            <div class="agreement-section">
                <h2>八、其他条款</h2>
                <p>8.1 本协议适用中华人民共和国法律，争议提交海南省琼海市人民法院管辖。</p>
                <p>8.2 隐私政策为本协议不可分割部分，点击查看隐私政策。</p>
            </div>

            <div class="agreement-section">
                <h2>九、联系我们</h2>
                <p>电话：400-889-0919</p>
                <p>传真：0898-36836706</p>
                <p>地址：海南省琼海市嘉积镇上埇嘉昌街5号</p>
                <p>手机：13976607717</p>
                <p>邮箱：<EMAIL></p>
            </div>
        </div>
    </div>
</template>

<script>
import NavBar from '../../components/NavBar.vue'

export default {
    components: {
        NavBar
    },
    data() {
        return {
            updateDate: '2025年4月29日',
            effectiveDate: '2025年4月29日'
        }
    }
}
</script>

<style lang="scss">
.agreement-page {
    background-color: #fff;
    min-height: 100vh;
    
    .agreement-content {
        padding: 20px;
        
        .agreement-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .agreement-date {
            font-size: 14px;
            color: #999;
            margin-bottom: 20px;
        }
        
        .agreement-section {
            margin-bottom: 20px;
            
            h2 {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #333;
            }
            
            p {
                font-size: 14px;
                line-height: 1.6;
                color: #666;
                margin-bottom: 10px;
            }
            
            ul {
                padding-left: 20px;
                margin-bottom: 10px;
                
                li {
                    font-size: 14px;
                    line-height: 1.6;
                    color: #666;
                    margin-bottom: 5px;
                }
            }
        }
    }
}
</style>