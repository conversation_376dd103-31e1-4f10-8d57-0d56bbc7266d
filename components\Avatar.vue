<template>
  <div
    class="avatar"
    :style="{ width: avatarSize + 'px', height: avatarSize + 'px' }"
    @click="handleAvatarClick"
    @longpress="longpress"
    @touchend="touchend"
  >
    <!-- 使用遮罩层避免android长按头像会出现保存图片的弹窗 -->
    <div class="img-mask"></div>
    <image
      :lazy-load="true"
      class="avatar-img"
      v-if="avatarUrl"
      :src="avatarUrl"
      mode="aspectFill"
      @error="handleAvatarError"
    />
    <div class="avatar-name-wrapper" :style="{ backgroundColor: color }">
      <div class="avatar-name-text" :style="{ fontSize: fontSize + 'px' }">
        {{ appellation }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { customNavigateTo, customRedirectTo } from '../utils/customNavigate'
import type { UserNameCard } from '@xkit-yx/im-store'
import { autorun } from 'mobx'
import { ref, computed, onUnmounted, onMounted, onActivated, defineProps, defineEmits } from '../utils/transformVue'
import { deepClone } from '../utils'
import { getImagePath } from '../utils/imageCache'

const props = defineProps({
  account: {
    type: String,
    required: true,
  },
  teamId: {
    type: String,
    default: '',
  },
  avatar: {
    type: String,
    default: '',
  },
  size: {
    type: String,
    default: '',
  },
  gotoUserCard: {
    type: Boolean,
    default: false,
  },
  fontSize: {
    type: String,
    default: '',
  },
  isRedirect: {
    type: Boolean,
    default: false,
  },
})
const $emit = defineEmits(['onLongpress'])

const avatarSize = props.size || 42
const user = ref<UserNameCard>()
let isLongPress = false // uniapp 长按事件也会触发点击事件，此时需要处理
// 组件挂载状态标记
const isComponentMounted = ref(false)
const appellation = computed(() => {
  // @ts-ignore
  return uni.$UIKitStore.uiStore
    .getAppellation({
      account: props.account,
      teamId: props.teamId,
      ignoreAlias: false,
      nickFromMsg: '',
    })
    .slice(0, 2)
})
const uninstallUserInfoWatch = autorun(async () => {
  // @ts-ignore
  const data = await uni.$UIKitStore?.userStore?.getUserActive(props.account)
  user.value = deepClone(data)
})

// 缓存的头像URL
const cachedAvatarUrl = ref('')

// 原始头像URL
const originalAvatarUrl = computed(() => {
  // @ts-ignore
  user.value = deepClone(uni.$UIKitStore?.userStore?.users?.get(props.account))
  return props.avatar || user.value?.avatar
})

// 用于显示的头像URL（优先使用缓存）
const avatarUrl = computed(() => {
  return cachedAvatarUrl.value || originalAvatarUrl.value
})

// 加载并缓存头像
const loadAndCacheAvatar = async () => {
  const url = originalAvatarUrl.value
  if (!url) return
  try {
    // 获取缓存的图片路径，添加错误处理
    const cachedPath = await getImagePath(url)

    if (cachedPath) {
      // 只有当组件仍然挂载时才更新URL
      if (isComponentMounted.value) {
        console.log(`[Avatar] 头像加载成功: account=${props.account}, path=${cachedPath}`);
        cachedAvatarUrl.value = cachedPath
      }
    }
  } catch (error) {
    console.error(`[Avatar] 加载头像缓存失败: account=${props.account}`, error)
    // 加载失败时，使用默认图片
    if (isComponentMounted.value) {
      cachedAvatarUrl.value = '/static/default-img.png'
    }
  }
}

const key = `__yx_avatar_color_${props.account}__`
let color = uni.getStorageSync(key)
if (!color) {
  const colorMap: { [key: number]: string } = {
    0: '#60CFA7',
    1: '#53C3F3',
    2: '#537FF4',
    3: '#854FE2',
    4: '#BE65D9',
    5: '#E9749D',
    6: '#F9B751',
  }
  const _color = colorMap[Math.floor(Math.random() * 7)]
  uni.setStorageSync(key, _color)
  color = _color
}

const handleAvatarClick = () => {
  if (props.gotoUserCard && !isLongPress) {
    if (props.isRedirect) {
      // @ts-ignore
      if (props.account === uni.$UIKitStore?.userStore?.myUserInfo.account) {
        customRedirectTo({
          url: `/pages/user-card/my-detail/index`,
        })
      } else {
        customRedirectTo({
          url: `/pages/user-card/friend/index?account=${props.account}`,
        })
      }
    } else {
      // @ts-ignore
      if (props.account === uni.$UIKitStore?.userStore?.myUserInfo.account) {
        customNavigateTo({
          url: `/pages/user-card/my-detail/index`,
        })
      } else {
        customNavigateTo({
          url: `/pages/user-card/friend/index?account=${props.account}`,
        })
      }
    }
  }
}

const longpress = () => {
  isLongPress = true
  $emit('onLongpress')
}

const touchend = () => {
  setTimeout(() => {
    isLongPress = false
  }, 200)
}

// 处理头像加载错误
const handleAvatarError = () => {
  console.log(`[Avatar] 头像加载失败，使用默认图片: account=${props.account}`)
  cachedAvatarUrl.value = '/static/default-img.png'
}

// 监听原始头像URL的变化
const uninstallAvatarWatch = autorun(() => {
  const url = originalAvatarUrl.value
  if (url) {
    loadAndCacheAvatar()
  }
})

// 组件挂载时加载缓存的头像
onMounted(() => {
  isComponentMounted.value = true
  loadAndCacheAvatar()
})

// 组件激活时（从缓存恢复）重新加载头像
onActivated(() => {
  console.log(`[Avatar] 组件激活: account=${props.account}`);
  isComponentMounted.value = true
  // 如果没有缓存的头像URL，重新加载
  if (!cachedAvatarUrl.value && originalAvatarUrl.value) {
    console.log(`[Avatar] 组件激活时重新加载头像: account=${props.account}`);
    loadAndCacheAvatar()
  }
})

// 组件卸载时清理监听
onUnmounted(() => {
  isComponentMounted.value = false
  uninstallUserInfoWatch()
  uninstallAvatarWatch()
})
</script>

<style scoped lang="scss">
.avatar {
  overflow: hidden;
  border-radius: 50%;
  flex-shrink: 0;
  position: relative;
}

.img-mask {
  position: absolute;
  z-index: 10;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  opacity: 0;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-name-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-name-text {
  color: #fff;
  size: 14px;
}
</style>
