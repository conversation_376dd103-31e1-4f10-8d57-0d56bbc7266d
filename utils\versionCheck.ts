/**
 * 版本检查工具函数
 */

interface VersionInfo {
  version: string
  versionCode: number
  updateUrl?: string
  updateContent?: string[]
  updateSize?: string
  forceUpdate?: boolean
}

/**
 * 获取当前应用版本
 */
export function getCurrentVersion(): string {
  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync()
  return systemInfo.appVersion || '1.0.0'
  // #endif

  return '1.0.0'
}

/**
 * 获取当前版本号
 */
export function getCurrentVersionCode(): number {
  // 这里可以从manifest.json或其他配置文件读取
  return 100
}

/**
 * 比较版本号
 * @param version1 版本1
 * @param version2 版本2
 * @returns 1: version1 > version2, 0: 相等, -1: version1 < version2
 */
export function compareVersion(version1: string, version2: string): number {
  const v1Parts = version1.split('.').map(Number)
  const v2Parts = version2.split('.').map(Number)

  const maxLength = Math.max(v1Parts.length, v2Parts.length)

  for (let i = 0; i < maxLength; i++) {
    const v1Part = v1Parts[i] || 0
    const v2Part = v2Parts[i] || 0

    if (v1Part > v2Part) return 1
    if (v1Part < v2Part) return -1
  }

  return 0
}

/**
 * 检查版本更新
 * @returns Promise<VersionInfo | null> 如果有更新返回版本信息，否则返回null
 */
export async function checkForUpdate(): Promise<VersionInfo | null> {
  // #ifdef APP-PLUS
  try {
    // 调用实际的API接口
    const { checkAppVersion } = await import('../api/index.js')

    const currentVersion = getCurrentVersion()
    const currentVersionCode = getCurrentVersionCode()

    const response = await checkAppVersion({
      currentVersion,
      currentVersionCode,
      platform: uni.getSystemInfoSync().platform
    })

    if (response && response.data) {
      const { version, versionCode, updateUrl, updateContent, updateSize, forceUpdate } = response.data

      if (compareVersion(version, currentVersion) > 0) {
        return {
          version,
          versionCode,
          updateUrl,
          updateContent,
          updateSize,
          forceUpdate
        }
      }
    }

    return null
  } catch (error) {
    console.error('检查版本更新失败:', error)

    // 如果API调用失败，返回模拟数据用于测试
    const mockResponse = {
      version: '1.0.1',
      versionCode: 101,
      updateUrl: 'https://example.com/download',
      updateContent: [
        '修复已知问题',
        '优化用户体验',
        '新增功能介绍页面',
        '提升应用稳定性'
      ],
      updateSize: '15.2 MB',
      forceUpdate: false
    }

    const currentVersion = getCurrentVersion()
    if (compareVersion(mockResponse.version, currentVersion) > 0) {
      return mockResponse
    }

    return null
  }
  // #endif

  // 非APP平台不支持版本检查
  return null
}

/**
 * 执行应用更新
 * @param updateUrl 更新下载链接
 */
export function performUpdate(updateUrl?: string) {
  // #ifdef APP-PLUS
  if (updateUrl) {
    // 打开应用商店或下载页面
    plus.runtime.openURL(updateUrl)
  } else {
    // 默认打开应用商店
    const packageName = plus.runtime.appid
    plus.runtime.openURL(`market://details?id=${packageName}`)
  }
  // #endif
}

/**
 * 自动检查更新（应用启动时调用）
 */
export async function autoCheckUpdate() {
  // #ifdef APP-PLUS
  try {
    const updateInfo = await checkForUpdate()
    if (updateInfo) {
      const title = updateInfo.forceUpdate ? '强制更新' : '发现新版本'
      const content = `发现新版本 ${updateInfo.version}，是否立即更新？`

      uni.showModal({
        title,
        content,
        showCancel: !updateInfo.forceUpdate,
        success: (res) => {
          if (res.confirm) {
            performUpdate(updateInfo.updateUrl)
          }
        }
      })
    }
  } catch (error) {
    console.error('自动检查更新失败:', error)
  }
  // #endif
}
