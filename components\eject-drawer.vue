/*----抽屉----*/
<template>
	<view catchtouchmove='true'>
		<view v-if="mask" class="tui-drawer-mask" :class="{'tui-drawer-mask_show':visible}" @tap="handleMaskClick">
		</view>
		<view class="tui-drawer-container"
			:class="[isRoundBox?'is_round_box':'','tui-drawer-container_' + mode,visible? mode + '_show':'']"
			:style="{ width: drawerWidth, height: drawerHeight }">
			<slot></slot>
		</view>
	</view>
</template>

<script>
	/**
	 * 超过一屏时插槽使用scroll-view
	 **/
	export default {
		name: "eject-drawer",
		props: {
			visible: {
				type: Boolean,
				default: false
			},
			isRoundBox: {
				type: Boolean,
				default: false
			},
			mask: {
				type: Boolean,
				default: true
			},
			maskClosable: {
				type: Boolean,
				default: true
			},
			mode: {
				type: String,
				default: 'left' // left right
			},
			drawerWidth: {
				type: String,
				default: '30%'
			},
			drawerHeight: {
				type: String,
				default: '100%'
			},
		},
		mounted() {},
		methods: {
			handleMaskClick() {
				if (!this.maskClosable) {
					return;
				}
				this.$emit('close', {});
			}
		}
	}
</script>

<style scoped>
	.tui-drawer-mask {
		opacity: 0;
		visibility: hidden;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 10;
		background-color: rgba(0, 0, 0, 0.6);
		transition: all 0.3s ease-in-out;
	}

	.tui-drawer-mask_show {
		display: block;
		visibility: visible;
		opacity: 1;
	}

	.tui-drawer-container {
		position: fixed;
		top: 0;
		transform: translate3d(-50%, -50%, 0);
		transform-origin: center;
		transition: all 0.3s ease-in-out;
		z-index: 1000;
		opacity: 0;
		overflow-y: scroll;
		background-color: #fff;
		-webkit--ms-scroll-limitoverflow-scrolling: touch;
		-ms-touch-action: pan-y cross-slide-y;
		-ms-scroll-chaining: none;
		-ms-scroll-limit: 0 50 0 50;
	}

	.tui-drawer-container_left {
		left: 0;
		top: 50%;
		transform: translate3d(-100%, -50%, 0);
	}

	.tui-drawer-container_right {
		right: 0;
		top: 50%;
		left: auto;
		transform: translate3d(100%, -50%, 0);
	}

	.tui-drawer-container_bottom {
		left: 50%;
		top: auto;
		bottom: 0;
		transform: translate3d(-50%, 100%, 0);
	}

	.tui-drawer-container_top {
		left: 50%;
		top: 0;
		transform: translate3d(-50%, -100%, 0);
	}

	.top_show {
		opacity: 1;
		transform: translate3d(-50%, 0, 0);
	}

	.bottom_show {
		opacity: 1;
		transform: translate3d(-50%, 0, 0);
	}

	.is_round_box {
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;
	}

	.left_show {
		opacity: 1;
		transform: translate3d(0, -50%, 0);
	}

	.right_show {
		opacity: 1;
		transform: translate3d(0, -50%, 0);
	}
</style>
