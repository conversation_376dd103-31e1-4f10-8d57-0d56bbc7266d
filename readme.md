# XChat即时通讯应用

XChat是一款基于uni-app开发的跨平台即时通讯应用，支持单聊、群聊、音视频通话等功能。项目集成了网易云信(NIM)SDK，提供稳定可靠的即时通讯服务。

## 技术栈

- 框架：Vue 2 + uni-app
- UI组件：Vant UI、@dcloudio/uni-ui
- 通讯SDK：网易云信 IM SDK、网易云信音视频通话SDK
- 状态管理：MobX
- 辅助工具：dayjs(时间处理)、pinyin(汉字转拼音)

## 功能特性

- 账号系统
  - 手机号+验证码登录
  - Token认证机制
  - 自动登录/退出
  
- 即时通讯
  - 单聊/群聊
  - 语音/视频/图片/文件消息
  - 消息撤回
  - @功能
  - 聊天记录管理
  
- 通讯录
  - 好友管理
  - 群组管理
  - 通讯录搜索与筛选
  
- 音视频通话
  - 一对一语音/视频通话
  - 通话记录
  
- 推送通知
  - 离线消息推送
  - 多厂商推送支持(VIVO、HONOR等)

## 项目结构

```
├── api/            # API接口定义
├── components/     # 公共组件
├── locale/         # 国际化文件
├── pages/          # 页面文件
│   ├── Bangding/   # 绑定相关页面
│   ├── Chat/       # 聊天相关页面
│   ├── Contact/    # 联系人页面
│   ├── Conversation/ # 会话列表页面
│   ├── Discover/   # 发现页面
│   ├── Friend/     # 好友相关页面
│   ├── Group/      # 群组相关页面
│   ├── Login/      # 登录页面
│   ├── index/      # 主页面
│   └── user-card/  # 用户资料卡页面
├── static/         # 静态资源
├── uni_modules/    # uni-app组件库
├── utils/          # 工具函数
├── nativeplugins/  # 原生插件
│   ├── netease-CallKit/ # 网易云信音视频通话插件
│   └── NIMUniPlugin/    # 网易云信IM插件
├── App.vue         # 应用入口
├── main.js         # 主文件
├── manifest.json   # 应用配置
├── pages.json      # 页面配置
└── uni.scss        # 全局样式
```

## 开发环境准备

1. **安装依赖**
   ```bash
   # 使用yarn
   yarn install
   
   # 或使用npm
   npm install
   ```

2. **开发工具**
   - 推荐使用HBuilderX作为IDE
   - 导入项目后可直接运行到模拟器或真机

3. **环境配置**
   - 确保`utils/config.js`中的API地址和AppKey配置正确
   - 多平台打包需配置好相应的开发者证书

## 运行与构建

### 开发模式运行

```bash
# HBuilderX
点击工具栏的运行按钮，选择需要运行的平台

# 命令行
npm run dev:h5      # 运行H5版本
npm run dev:app     # 运行APP版本
```

### 打包构建

```bash
# HBuilderX
点击工具栏的发行按钮，选择需要发布的平台

# 命令行
npm run build:h5    # 构建H5版本
npm run build:app   # 构建APP版本
```

## 关键配置说明

### 网易云信配置

在`utils/config.js`文件中配置：

```javascript
// 网易云信AppKey
export const APP_KEY = "your_app_key";

// API服务器地址
export const APIURL = "your_api_base_url";

// 客户端ID
export const CLIENT_ID = "your_client_id";
```

### 推送配置

在`manifest.json`中配置各厂商推送参数：

```json
"push": {
  "PUSH_VIVO_APPID": "your_vivo_app_id",
  "PUSH_VIVO_APPKEY": "your_vivo_app_key",
  "PUSH_HONOR_APPID": "your_honor_app_id"
}
```

## 业务流程

1. **用户登录流程**
   - 用户输入手机号获取验证码
   - 验证通过后获取token
   - 使用token初始化网易云信SDK
   - 连接成功后进入主界面

2. **消息收发流程**
   - 消息通过网易云信SDK收发
   - 消息类型包括：文本、图片、语音、视频、自定义消息等
   - 消息状态同步至服务器

3. **音视频通话流程**
   - 调用网易云信CallKit发起通话
   - 通话界面由CallKit原生插件提供
   - 通话记录存储在本地和服务器

## 常见问题

1. **Token过期处理**
   - 系统会自动检测token过期
   - 过期后会清除登录状态并跳转到登录页

2. **消息推送问题**
   - 确保manifest.json中配置了正确的推送参数
   - 不同厂商手机需要配置不同的推送服务

3. **音视频通话问题**
   - 音视频通话依赖网易云信原生插件
   - 需要确保相关权限已正确授予

## 开发注意事项

1. **平台差异**
   - 使用条件编译(`#ifdef`)处理不同平台差异
   - H5、App、小程序等平台功能支持不同

2. **原生插件限制**
   - 网易云信音视频通话功能仅支持App端
   - 小程序和H5平台不支持完整的音视频通话功能

3. **性能优化**
   - 大量消息加载时注意分页处理
   - 图片等资源使用懒加载
   - 避免过多的全局状态更新

## 方法标识

`#my：`为自己拓展代码
